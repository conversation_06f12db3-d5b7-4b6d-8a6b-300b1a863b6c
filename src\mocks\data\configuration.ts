// Configuration mock data for MSW
import type {
  AppConfiguration,
  FeatureFlags
} from '../../types/configuration';

// Re-export centralized types for mock data compatibility
export type { AppConfiguration, FeatureFlags } from '../../types/configuration';

// Legacy simplified configuration interface for mock data
export interface MockAppConfiguration {
  appName: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
  apiBaseUrl: string;
  maxFileUploadSize: number;
  sessionTimeout: number;
  defaultLanguage: string;
  supportedLanguages: string[];
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  itemsPerPage: number;
  maxRetries: number;
  retryDelay: number;
}

export interface SystemSettings {
  maintenance: {
    enabled: boolean;
    message?: string;
    scheduledStart?: string;
    scheduledEnd?: string;
  };
  security: {
    passwordMinLength: number;
    passwordRequireSpecialChars: boolean;
    sessionTimeoutWarning: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  ui: {
    defaultTheme: 'light' | 'dark' | 'system';
    allowThemeToggle: boolean;
    sidebarCollapsed: boolean;
    compactMode: boolean;
    showTooltips: boolean;
    animationsEnabled: boolean;
    reducedMotion: boolean;
    highContrastMode: boolean;
    itemsPerPage: number;
    maxItemsPerPage: number;
    enableVirtualScrolling: boolean;
    enableScreenReader: boolean;
    enableKeyboardNavigation: boolean;
    focusIndicatorVisible: boolean;
  };
  performance: {
    enableCaching: boolean;
    cacheTimeout: number;
    enableLazyLoading: boolean;
    enableCodeSplitting: boolean;
    preloadCriticalResources: boolean;
    enableImageOptimization: boolean;
    enableAssetCompression: boolean;
  };
}

// Default feature flags
export const defaultFeatureFlags: FeatureFlags = {
  enableAnalytics: true,
  enableNotifications: true,
  enableDarkMode: true,
  enableBetaFeatures: false,
  enableAdvancedSearch: true,
  enableRealTimeUpdates: true,
  enableExperimentalUI: false,
  enableDebugMode: false,
  enableSSOLogin: false,
  enableAuditLogging: true,
  enableDataExport: true,
  enableAdvancedReporting: false,
};

// Default application configuration for mock data
export const defaultConfiguration: MockAppConfiguration = {
  appName: 'Nexed Web',
  version: '1.0.0',
  environment: 'development',
  apiBaseUrl: '/api',
  maxFileUploadSize: 10485760, // 10MB
  sessionTimeout: 3600000, // 1 hour in milliseconds
  defaultLanguage: 'en',
  supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja'],
  dateFormat: 'MM/dd/yyyy',
  timeFormat: '12h',
  timezone: 'UTC',
  itemsPerPage: 25,
  maxRetries: 3,
  retryDelay: 1000,
};

// Default system settings
export const defaultSystemSettings: SystemSettings = {
  maintenance: {
    enabled: false,
  },
  security: {
    passwordMinLength: 8,
    passwordRequireSpecialChars: true,
    sessionTimeoutWarning: 300000, // 5 minutes
    maxLoginAttempts: 5,
    lockoutDuration: 900000, // 15 minutes
  },
  ui: {
    defaultTheme: 'system',
    allowThemeToggle: true,
    sidebarCollapsed: false,
    compactMode: false,
    showTooltips: true,
    animationsEnabled: true,
    reducedMotion: false,
    highContrastMode: false,
    itemsPerPage: 25,
    maxItemsPerPage: 100,
    enableVirtualScrolling: true,
    enableScreenReader: true,
    enableKeyboardNavigation: true,
    focusIndicatorVisible: true,
  },
  performance: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    enableLazyLoading: true,
    enableCodeSplitting: true,
    preloadCriticalResources: true,
    enableImageOptimization: true,
    enableAssetCompression: true,
  },
};

// Environment-specific configurations
export const getEnvironmentConfig = (environment: string) => {
  switch (environment) {
    case 'development':
      return {
        featureFlags: {
          ...defaultFeatureFlags,
          enableDebugMode: true,
          enableBetaFeatures: true,
          enableExperimentalUI: true,
        },
        configuration: {
          ...defaultConfiguration,
          environment: 'development' as const,
        },
        systemSettings: {
          ...defaultSystemSettings,
          security: {
            ...defaultSystemSettings.security,
            passwordMinLength: 4,
            maxLoginAttempts: 10,
          },
        },
      };

    case 'staging':
      return {
        featureFlags: {
          ...defaultFeatureFlags,
          enableBetaFeatures: true,
          enableAdvancedReporting: true,
        },
        configuration: {
          ...defaultConfiguration,
          environment: 'staging' as const,
          apiBaseUrl: '/api/staging',
        },
        systemSettings: defaultSystemSettings,
      };

    case 'production':
      return {
        featureFlags: {
          ...defaultFeatureFlags,
          enableSSOLogin: true,
          enableAdvancedReporting: true,
        },
        configuration: {
          ...defaultConfiguration,
          environment: 'production' as const,
          apiBaseUrl: '/api/v1',
          sessionTimeout: 7200000, // 2 hours
        },
        systemSettings: {
          ...defaultSystemSettings,
          security: {
            ...defaultSystemSettings.security,
            passwordMinLength: 12,
            maxLoginAttempts: 3,
            lockoutDuration: 1800000, // 30 minutes
          },
        },
      };

    default:
      return {
        featureFlags: defaultFeatureFlags,
        configuration: defaultConfiguration,
        systemSettings: defaultSystemSettings,
      };
  }
};

// Helper functions
export const getFeatureFlags = (environment?: string): FeatureFlags => {
  return getEnvironmentConfig(environment || 'development').featureFlags;
};

export const getConfiguration = (environment?: string): AppConfiguration => {
  return getEnvironmentConfig(environment || 'development').configuration;
};

export const getSystemSettings = (environment?: string): SystemSettings => {
  return getEnvironmentConfig(environment || 'development').systemSettings;
};

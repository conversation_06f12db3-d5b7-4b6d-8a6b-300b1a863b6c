// User Types
// Centralized type definitions for user-related entities

import type { ReactNode } from 'react';
import type { Theme, Language, TimeFormat, DateFormat } from './app';

// User Status
export type UserStatus = 'online' | 'offline' | 'away' | 'busy' | 'invisible';

// User Role
export type UserRole = 'user' | 'admin' | 'moderator' | 'viewer' | 'editor' | 'manager' | 'owner';

// Account Status
export type AccountStatus = 'active' | 'inactive' | 'pending' | 'suspended' | 'banned' | 'deleted';

// Core User Interface
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: ReactNode | string;
  role: UserRole;
  status?: UserStatus;
  accountStatus?: AccountStatus;
  createdAt?: Date;
  updatedAt?: Date;
  lastLoginAt?: Date;
  lastSeenAt?: Date;
  permissions: string[];
  preferences: UserPreferences;
  profile: UserProfile;
  notifications: UserNotification[];
  settings: UserSettings;
}

// User Profile
export interface UserProfile {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  bio?: string;
  title?: string;
  department?: string;
  company?: string;
  location?: string;
  timezone?: string;
  phone?: string;
  website?: string;
  socialLinks?: SocialLink[];
  customFields?: Record<string, any>;
}

// Social Link
export interface SocialLink {
  platform: string;
  url: string;
  username?: string;
}

// User Preferences
export interface UserPreferences {
  theme: Theme;
  language: Language;
  dateFormat: DateFormat;
  timeFormat: TimeFormat;
  timezone: string;
  enableNotifications: boolean;
  enableEmailNotifications: boolean;
  enablePushNotifications: boolean;
  enableSoundNotifications: boolean;
  notificationFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  autoSave: boolean;
  compactMode: boolean;
  showTooltips: boolean;
  enableAnimations: boolean;
  enableKeyboardShortcuts: boolean;
  defaultView: string;
  itemsPerPage: number;
}

// User Settings
export interface UserSettings {
  privacy: PrivacySettings;
  security: SecuritySettings;
  accessibility: AccessibilitySettings;
  integrations: IntegrationSettings;
}

// Privacy Settings
export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'contacts';
  showOnlineStatus: boolean;
  showLastSeen: boolean;
  allowDirectMessages: boolean;
  allowMentions: boolean;
  shareAnalytics: boolean;
  shareUsageData: boolean;
}

// Security Settings
export interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  allowMultipleSessions: boolean;
  requirePasswordChange: boolean;
  passwordChangeInterval: number;
  loginNotifications: boolean;
  suspiciousActivityAlerts: boolean;
  allowedIpAddresses?: string[];
}

// Accessibility Settings
export interface AccessibilitySettings {
  highContrastMode: boolean;
  reducedMotion: boolean;
  screenReaderSupport: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  colorBlindSupport: boolean;
}

// Integration Settings
export interface IntegrationSettings {
  enabledIntegrations: string[];
  integrationConfigs: Record<string, any>;
  webhookUrls: string[];
  apiKeys: Record<string, string>;
}

// User Notification
export interface UserNotification {
  id: string;
  type: 'system' | 'app' | 'mention' | 'message' | 'task' | 'reminder';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isArchived: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  source: string;
  sourceId?: string;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
}

// Notification Action
export interface NotificationAction {
  id: string;
  label: string;
  action: string;
  isPrimary?: boolean;
  isDestructive?: boolean;
}

// Permission
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  scope?: 'global' | 'app' | 'module' | 'resource';
  conditions?: PermissionCondition[];
}

// Permission Condition
export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: any;
}

// User Session
export interface UserSession {
  id: string;
  userId: string;
  token: string;
  refreshToken?: string;
  expiresAt: Date;
  createdAt: Date;
  lastActivityAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  deviceInfo?: DeviceInfo;
}

// Device Info
export interface DeviceInfo {
  type: 'desktop' | 'mobile' | 'tablet';
  os: string;
  browser: string;
  version: string;
  isTrusted: boolean;
}

// User Activity
export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, any>;
}

// User Authentication
export interface AuthCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  twoFactorCode?: string;
}

// User Registration
export interface UserRegistration {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  subscribeNewsletter?: boolean;
  inviteCode?: string;
}

// User Update Request
export interface UserUpdateRequest {
  name?: string;
  email?: string;
  profile?: Partial<UserProfile>;
  preferences?: Partial<UserPreferences>;
  settings?: Partial<UserSettings>;
}

// User Creation Request
export interface UserCreationRequest {
  name: string;
  email: string;
  role: UserRole;
  sendInvitation?: boolean;
  permissions?: string[];
  profile?: Partial<UserProfile>;
  preferences?: Partial<UserPreferences>;
}

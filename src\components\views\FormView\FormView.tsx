import React, { useState, useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { FormViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface FormField {
  name: string;
  label: string;
  type: string;
  value: any;
  required?: boolean;
  readonly?: boolean;
  section?: string;
  options?: { label: string; value: any }[];
  placeholder?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface FormSection {
  name: string;
  label: string;
  collapsible?: boolean;
  collapsed?: boolean;
  fields: FormField[];
}

export interface FormViewProps extends BaseViewProps {
  config: FormViewConfig;
  record?: any;
  onFieldChange?: (fieldName: string, value: any) => void;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  onDelete?: () => void;
  renderField?: (field: FormField) => React.ReactNode;
  renderSection?: (section: FormSection) => React.ReactNode;
  isEditing?: boolean;
  isSaving?: boolean;
}

export const FormView: React.FC<FormViewProps> = ({
  data,
  config,
  record,
  onFieldChange,
  onSave,
  onCancel,
  onDelete,
  renderField,
  renderSection,
  isEditing = true,
  isSaving = false,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();
  const [formData, setFormData] = useState<any>(record || {});
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set(config.sections?.filter(s => s.collapsed).map(s => s.name) || [])
  );
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Organize fields into sections
  const sections = useMemo((): FormSection[] => {
    const sectionMap = new Map<string, FormSection>();

    // Initialize sections
    config.sections?.forEach(section => {
      sectionMap.set(section.name, {
        name: section.name,
        label: section.label,
        collapsible: section.collapsible,
        collapsed: section.collapsed,
        fields: [],
      });
    });

    // Add default section if no sections defined
    if (sectionMap.size === 0) {
      sectionMap.set('default', {
        name: 'default',
        label: 'Details',
        collapsible: false,
        collapsed: false,
        fields: [],
      });
    }

    // Organize fields into sections
    config.fields.forEach(fieldConfig => {
      const sectionName = fieldConfig.section || 'default';
      let section = sectionMap.get(sectionName);

      if (!section) {
        section = {
          name: sectionName,
          label: sectionName,
          collapsible: false,
          collapsed: false,
          fields: [],
        };
        sectionMap.set(sectionName, section);
      }

      const field: FormField = {
        name: fieldConfig.name,
        label: fieldConfig.label,
        type: fieldConfig.type,
        value: formData[fieldConfig.name],
        required: fieldConfig.required,
        readonly: fieldConfig.readonly || !isEditing,
        section: sectionName,
      };

      section.fields.push(field);
    });

    return Array.from(sectionMap.values());
  }, [config, formData, isEditing]);

  const handleFieldChange = (fieldName: string, value: any) => {
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);
    onFieldChange?.(fieldName, value);

    // Clear error for this field
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const validateField = (field: FormField): string | null => {
    if (field.required && (!field.value || field.value === '')) {
      return `${field.label} is required`;
    }

    if (field.validation) {
      const { min, max, pattern, message } = field.validation;

      if (min !== undefined && field.value < min) {
        return message || `${field.label} must be at least ${min}`;
      }

      if (max !== undefined && field.value > max) {
        return message || `${field.label} must be at most ${max}`;
      }

      if (
        pattern &&
        typeof field.value === 'string' &&
        !new RegExp(pattern).test(field.value)
      ) {
        return message || `${field.label} format is invalid`;
      }
    }

    return null;
  };

  const handleSave = () => {
    const newErrors: { [key: string]: string } = {};

    // Validate all fields
    sections.forEach(section => {
      section.fields.forEach(field => {
        const error = validateField(field);
        if (error) {
          newErrors[field.name] = error;
        }
      });
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      onSave?.(formData);
    }
  };

  const toggleSection = (sectionName: string) => {
    setCollapsedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionName)) {
        newSet.delete(sectionName);
      } else {
        newSet.add(sectionName);
      }
      return newSet;
    });
  };

  const renderFormField = (field: FormField) => {
    if (renderField) {
      return renderField(field);
    }

    const hasError = !!errors[field.name];
    const commonProps = {
      id: field.name,
      value: field.value || '',
      onChange: (
        e: React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
      ) => handleFieldChange(field.name, e.target.value),
      disabled: field.readonly || isSaving,
      className: cn(
        'w-full px-3 py-2 border rounded-md transition-colors',
        hasError
          ? 'border-red-500 focus:border-red-500'
          : 'border-gray-300 focus:border-blue-500',
        field.readonly && 'bg-gray-50'
      ),
      style: {
        borderColor: hasError ? '#ef4444' : colors.border,
        backgroundColor: field.readonly ? colors.muted : colors.background,
        color: colors.text,
      },
    };

    let input: React.ReactNode;

    switch (field.type) {
      case 'textarea':
        input = (
          <textarea {...commonProps} rows={4} placeholder={field.placeholder} />
        );
        break;
      case 'select':
        input = (
          <select {...commonProps}>
            <option value="">Select {field.label}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
        break;
      case 'checkbox':
        input = (
          <input
            type="checkbox"
            id={field.name}
            checked={!!field.value}
            onChange={e => handleFieldChange(field.name, e.target.checked)}
            disabled={field.readonly || isSaving}
            className="rounded"
          />
        );
        break;
      default:
        input = (
          <input
            {...commonProps}
            type={field.type}
            placeholder={field.placeholder}
          />
        );
    }

    return (
      <div key={field.name} className="space-y-1">
        <label
          htmlFor={field.name}
          className="block text-sm font-medium"
          style={{ color: colors.text }}
        >
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        {input}
        {hasError && (
          <p className="text-sm text-red-500">{errors[field.name]}</p>
        )}
      </div>
    );
  };

  const renderFormSection = (section: FormSection) => {
    if (renderSection) {
      return renderSection(section);
    }

    const isCollapsed = collapsedSections.has(section.name);

    return (
      <div key={section.name} className="space-y-4">
        {/* Section header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
            {section.label}
          </h3>
          {section.collapsible && (
            <button
              onClick={() => toggleSection(section.name)}
              className="p-1 hover:bg-gray-100 rounded"
              style={{ color: colors.textSecondary }}
            >
              {isCollapsed ? '▶' : '▼'}
            </button>
          )}
        </div>

        {/* Section fields */}
        {!isCollapsed && (
          <div
            className={cn(
              'grid gap-4',
              config.layout === 'two-column'
                ? 'grid-cols-1 md:grid-cols-2'
                : 'grid-cols-1'
            )}
          >
            {section.fields.map(renderFormField)}
          </div>
        )}
      </div>
    );
  };

  const renderFormActions = () => (
    <div
      className="flex items-center justify-between pt-6 border-t"
      style={{ borderTopColor: colors.border }}
    >
      <div>
        {onDelete && (
          <button
            onClick={onDelete}
            disabled={isSaving}
            className="px-4 py-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            Delete
          </button>
        )}
      </div>
      <div className="flex space-x-3">
        {onCancel && (
          <button
            onClick={onCancel}
            disabled={isSaving}
            className="px-4 py-2 border rounded-md hover:bg-gray-50 transition-colors"
            style={{ borderColor: colors.border, color: colors.text }}
          >
            Cancel
          </button>
        )}
        {onSave && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-4 py-2 text-white rounded-md hover:opacity-90 transition-opacity"
            style={{ backgroundColor: colors.primary }}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </button>
        )}
      </div>
    </div>
  );

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="max-w-4xl mx-auto p-6">
        <div
          className="bg-white rounded-lg border p-6 space-y-6"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
        >
          {sections.map(renderFormSection)}
          {(onSave || onCancel || onDelete) && renderFormActions()}
        </div>
      </div>
    </BaseView>
  );
};

export default FormView;

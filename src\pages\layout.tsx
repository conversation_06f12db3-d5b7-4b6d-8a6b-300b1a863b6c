import { Outlet } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { initializeApp } from '../utils/appInitializer';
import { ErrorBoundary } from '../components/common';

export default function RootLayout() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize app on start
  useEffect(() => {
    console.log('RootLayout: Starting app initialization...');
    initializeApp()
      .then(result => {
        console.log('RootLayout: App initialization result:', result);
        setIsLoading(false);
      })
      .catch(err => {
        console.error('RootLayout: App initialization failed:', err);
        setError(err.message || 'Failed to initialize app');
        setIsLoading(false);
      });
  }, []);

  // Show loading state while app is initializing
  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">
            Initializing application...
          </p>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed
  if (error) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <h1 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            Initialization Error
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  console.log('RootLayout: Rendering outlet...');
  return (
    <ErrorBoundary
      level="page"
      componentName="PageContent"
      enableAutoRecovery={true}
      enableReporting={true}
    >
      <Outlet />
    </ErrorBoundary>
  );
}

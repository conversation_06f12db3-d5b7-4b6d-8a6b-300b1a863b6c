// Main Components Export
// This file provides a centralized export for all component categories
// Import components by category for better tree-shaking and organization

// Global Components - Highly reusable components used across modules
export * from './global';

// UI Components - Basic building blocks (some deprecated, use global/ or forms/ instead)
export * from './ui';

// Layout Components - Structural components
export * from './layout';

// Form Components - Form and input handling
export * from './forms';

// Navigation Components - Navigation and routing
export * from './navigation';

// Feedback Components - User feedback and notifications
export * from './feedback';

// Data Display Components - Data presentation
export * from './data-display';

// Chart Components - Data visualization
export * from './charts';

// Views Components - All view types for data display
export * from './views';

// Common Components - Shared utilities
export * from './common';

// Icon Components - Professional single-color icons
export * from './icons';

// Dashboard Components - Main dashboard interface
export * from './Dashboard';

// Authentication Components - Login, registration, access control
export * from './auth';

// Security Components - CAPTCHA, verification, etc.
export * from './security';

// User Management Module (migrated to modules/user-management)
// Import from: import { AddUserModal, UserList } from '../modules/user-management';

import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';

export interface ContentPlaceholderProps {
  title: string;
  content: string;
  icon: React.ReactNode;
  appColor: string;
  onGetStarted?: () => void;
  className?: string;
  'data-testid'?: string;
}

const ContentPlaceholder: React.FC<ContentPlaceholderProps> = ({
  title,
  content,
  icon,
  appColor,
  onGetStarted,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div
      className={`rounded-lg border p-6 ${className}`}
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      <div className="text-center py-12">
        <div
          className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl"
          style={{ backgroundColor: appColor }}
        >
          {icon}
        </div>
        <h3
          className="text-lg font-semibold mb-2"
          style={{ color: colors.text }}
        >
          {title}
        </h3>
        <p
          className="text-sm max-w-md mx-auto"
          style={{ color: colors.textSecondary }}
        >
          {content}
        </p>
        {onGetStarted && (
          <div className="mt-6">
            <button
              className="px-4 py-2 rounded-lg text-white font-medium"
              style={{ backgroundColor: appColor }}
              onClick={onGetStarted}
            >
              Get Started
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentPlaceholder;

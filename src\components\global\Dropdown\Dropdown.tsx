import React, { useState } from 'react';
import { DropdownBase } from './DropdownBase';
import { DropdownTrigger } from './DropdownTrigger';
import { DropdownContent } from './DropdownContent';
import { DropdownItem } from './DropdownItem';
import { DropdownSection } from './DropdownSection';
import { DropdownSeparator } from './DropdownSeparator';
import type { DropdownItem as DropdownItemType, BaseDropdownProps } from './types';

export interface DropdownProps extends Omit<BaseDropdownProps, 'isOpen' | 'onOpenChange'> {
  trigger: React.ReactNode;
  items: DropdownItemType[];
  sections?: never; // Use either items or sections, not both
}

export interface DropdownWithSectionsProps extends Omit<BaseDropdownProps, 'isOpen' | 'onOpenChange'> {
  trigger: React.ReactNode;
  sections: Array<{
    id: string;
    title?: string;
    icon?: React.ReactNode;
    items: DropdownItemType[];
    collapsible?: boolean;
    defaultCollapsed?: boolean;
  }>;
  items?: never; // Use either items or sections, not both
}

/**
 * Complete dropdown component that combines all dropdown parts
 * Supports both simple item lists and sectioned content
 */
export const Dropdown: React.FC<DropdownProps | DropdownWithSectionsProps> = ({
  trigger,
  items,
  sections,
  ...baseProps
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const renderItems = (itemList: DropdownItemType[]) => {
    return itemList.map((item) => {
      if (item.isDivider) {
        return <DropdownSeparator key={item.id} />;
      }

      return (
        <DropdownItem
          key={item.id}
          icon={item.icon}
          shortcut={item.shortcut}
          description={item.description}
          disabled={item.disabled}
          onSelect={item.onClick}
        >
          {item.label}
        </DropdownItem>
      );
    });
  };

  return (
    <DropdownBase
      {...baseProps}
      isOpen={isOpen}
      onOpenChange={setIsOpen}
    >
      <DropdownTrigger>{trigger}</DropdownTrigger>
      
      <DropdownContent>
        {sections ? (
          // Render sectioned content
          sections.map((section, index) => (
            <React.Fragment key={section.id}>
              {index > 0 && <DropdownSeparator />}
              <DropdownSection
                title={section.title}
                icon={section.icon}
                collapsible={section.collapsible}
                defaultCollapsed={section.defaultCollapsed}
              >
                {renderItems(section.items)}
              </DropdownSection>
            </React.Fragment>
          ))
        ) : (
          // Render simple item list
          items && renderItems(items)
        )}
      </DropdownContent>
    </DropdownBase>
  );
};

// Export individual components for advanced composition
export {
  DropdownBase,
  DropdownTrigger,
  DropdownContent,
  DropdownItem,
  DropdownSection,
  DropdownSeparator,
};

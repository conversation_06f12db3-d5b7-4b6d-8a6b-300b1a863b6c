# MSW (Mock Service Worker) Infrastructure

This directory contains a comprehensive Mock Service Worker setup for the Nexed Web application. The infrastructure provides robust API mocking capabilities for development, testing, and demonstration purposes.

## 📁 Directory Structure

```
src/mocks/
├── README.md                 # This documentation
├── browser.ts               # MSW browser worker setup
├── node.ts                  # MSW Node.js setup (for testing)
├── handlers.ts              # Main handlers export
├── config.ts                # MSW configuration management
├── scenarios.ts             # Predefined testing scenarios
├── data/                    # Mock data organized by domain
│   ├── index.ts            # Central data exports
│   ├── users.ts            # User and authentication data
│   ├── companies.ts        # Company data
│   ├── apps.ts             # Application data
│   └── configuration.ts    # App configuration data
├── handlers/               # API handlers organized by domain
│   ├── auth.ts            # Authentication endpoints
│   ├── users.ts           # User management endpoints
│   ├── companies.ts       # Company management endpoints
│   ├── apps.ts            # Application management endpoints
│   └── configuration.ts   # Configuration endpoints
└── utils/                 # MSW utilities and helpers
    └── index.ts           # Utility functions
```

## 🚀 Features

### Comprehensive API Coverage

- **Authentication**: Login, logout, token refresh, password reset
- **User Management**: CRUD operations with permissions and roles
- **Company Management**: Multi-tenant company data
- **Application Management**: App catalog and configuration
- **System Configuration**: Feature flags, settings, and environment configs

### Advanced Capabilities

- **Realistic Network Simulation**: Configurable delays and error rates
- **Permission-Based Access Control**: Role and permission validation
- **Pagination Support**: Consistent pagination across all endpoints
- **Search and Filtering**: Advanced query capabilities
- **Error Simulation**: Configurable error scenarios for testing
- **Environment-Specific Configurations**: Different setups per environment

### Developer Experience

- **Scenario Management**: Predefined scenarios for different testing needs
- **Runtime Configuration**: Dynamic configuration updates
- **Comprehensive Logging**: Detailed request/response logging
- **Browser Console Integration**: Easy scenario switching via console

## 🛠️ Usage

### Basic Setup

The MSW infrastructure is automatically initialized in development mode. No additional setup is required for basic usage.

### Switching Scenarios

You can switch between different scenarios using URL parameters or the browser console:

```javascript
// Via URL parameter
// http://localhost:3000?mswScenario=errorTesting

// Via browser console
window.mswScenarios.apply('slowNetwork');
window.mswScenarios.current(); // Get current scenario
window.mswScenarios.available(); // List all scenarios
```

### Available Scenarios

- **development**: Standard development with moderate delays
- **fastTesting**: Minimal delays for fast test execution
- **errorTesting**: High error rates for error handling tests
- **slowNetwork**: Simulate slow network conditions
- **offline**: Simulate offline conditions
- **production**: Fast responses, minimal errors
- **demo**: Optimized for demonstrations
- **loadTesting**: Variable delays and errors

### Configuration

Access and modify MSW configuration:

```javascript
// Get current configuration
const config = window.mswScenarios.config();

// Available configuration options:
// - enabled: boolean
// - baseDelay: number (ms)
// - maxDelay: number (ms)
// - errorRate: number (0-1)
// - enableLogging: boolean
// - enableErrorSimulation: boolean
// - enableNetworkDelay: boolean
```

## 🔧 API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/dev-login` - Development login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Reset password
- `POST /api/auth/change-password` - Change password

### Users

- `GET /api/users` - List users (with pagination, search, filters)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `POST /api/users/bulk` - Bulk operations

### Companies

- `GET /api/companies` - List companies
- `GET /api/companies/active` - List active companies
- `GET /api/companies/:id` - Get company by ID
- `POST /api/companies` - Create company
- `PUT /api/companies/:id` - Update company
- `DELETE /api/companies/:id` - Delete company
- `GET /api/companies/stats` - Company statistics

### Applications

- `GET /api/apps` - List applications
- `GET /api/apps/active` - List active applications
- `GET /api/apps/categories` - Get app categories
- `GET /api/apps/:id` - Get app by ID
- `GET /api/apps/:id/views/:viewId` - Get app view content
- `POST /api/apps` - Create application
- `PUT /api/apps/:id` - Update application
- `DELETE /api/apps/:id` - Delete application
- `GET /api/apps/stats` - Application statistics

### Configuration

- `GET /api/config` - Get all configuration
- `GET /api/config/features` - Get feature flags
- `PUT /api/config/features` - Update feature flags
- `GET /api/config/app` - Get app configuration
- `PUT /api/config/app` - Update app configuration
- `GET /api/config/system` - Get system settings
- `PUT /api/config/system` - Update system settings
- `POST /api/config/reset` - Reset configuration
- `GET /api/config/health` - Health check

## 🧪 Testing

### Unit Tests

Mock data and handlers can be imported for unit testing:

```typescript
import { users, companies, apps } from '../mocks/data';
import { authHandlers } from '../mocks/handlers/auth';
```

### Integration Tests

Use the `fastTesting` scenario for integration tests:

```typescript
import { applyScenario } from '../mocks/scenarios';

beforeEach(() => {
  applyScenario('fastTesting');
});
```

### Error Testing

Use the `errorTesting` scenario to test error handling:

```typescript
import { applyScenario } from '../mocks/scenarios';

test('handles API errors gracefully', async () => {
  applyScenario('errorTesting');
  // Your test code here
});
```

## 🔒 Security

The MSW infrastructure includes:

- JWT token validation simulation
- Permission-based access control
- Role-based authorization
- Session management
- Rate limiting simulation

## 🌍 Environment Configuration

Different environments have different default configurations:

- **Development**: Moderate delays, some errors, full logging
- **Staging**: Realistic delays, minimal errors, logging enabled
- **Production**: MSW disabled by default
- **Test**: No delays, no errors, minimal logging

## 📝 Adding New Endpoints

1. **Add mock data** in `src/mocks/data/`
2. **Create handlers** in `src/mocks/handlers/`
3. **Export handlers** in `src/mocks/handlers.ts`
4. **Update types** if needed

Example:

```typescript
// src/mocks/data/products.ts
export const products = [{ id: '1', name: 'Product 1', price: 100 }];

// src/mocks/handlers/products.ts
export const productHandlers = [
  http.get('/api/products', async () => {
    return HttpResponse.json(products);
  }),
];

// src/mocks/handlers.ts
export const handlers = [
  ...authHandlers,
  ...productHandlers, // Add new handlers
];
```

## 🐛 Troubleshooting

### MSW Not Starting

- Check browser console for errors
- Ensure MSW service worker is registered
- Verify configuration is enabled

### Requests Not Being Intercepted

- Check if the URL matches handler patterns
- Verify MSW is running in development mode
- Check network tab for actual vs mocked requests

### Performance Issues

- Use `fastTesting` scenario for tests
- Reduce `baseDelay` and `maxDelay` in configuration
- Disable error simulation if not needed

## 📚 Resources

- [MSW Documentation](https://mswjs.io/)
- [MSW Browser Integration](https://mswjs.io/docs/getting-started/integrate/browser)
- [MSW Node.js Integration](https://mswjs.io/docs/getting-started/integrate/node)

import React, { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../layout/DynamicAppHeader';
import AppDynamicContent from '../layout/AppDynamicContent';
import { AppNotFound, Breadcrumb } from '../global';
import { ContentPlaceholder } from './ContentPlaceholder';
import { useViewData, useAppHeaderData } from './hooks';
import { updateNavLinksActiveState, getCurrentView } from './utils';
import { mockUserData } from './constants';
import { cn } from '../../utils/cn';
import { getAppById } from '../../data/mockApps';

export interface DynamicAppViewProps {
  className?: string;
  'data-testid'?: string;
}

const DynamicAppView: React.FC<DynamicAppViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { colors } = useThemeStore();

  const menuId = searchParams.get('menu');
  const viewId = searchParams.get('view') || 'dashboard';

  // Get app data based on menu ID
  const appData = menuId ? getAppById(menuId) : null;

  // Redirect to dashboard if no valid app is found
  useEffect(() => {
    if (!menuId || !appData) {
      navigate('/dashboard');
    }
  }, [menuId, appData, navigate]);

  if (!appData) {
    return <AppNotFound />;
  }

  // Update nav links to show active state based on current view
  const updatedNavLinks = updateNavLinksActiveState(appData, menuId!, viewId);
  const currentView = getCurrentView(appData, viewId);

  // Create view data and app header data using custom hooks
  const viewData = useViewData(currentView.title);
  const appHeaderData = useAppHeaderData(appData, updatedNavLinks);

  return (
    <div
      className={cn('min-h-screen', className)}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Dynamic App Header - Top navigation only */}
      <DynamicAppHeader
        app={appHeaderData}
        user={mockUserData}
      />

      {/* App Dynamic Content - Bottom bar + main content */}
      <AppDynamicContent view={viewData}>
        <div className="space-y-6">
          {/* Breadcrumb */}
          <Breadcrumb
            items={[
              {
                label: 'Dashboard',
                onClick: () => navigate('/dashboard'),
              },
              {
                label: appData.title,
              },
              {
                label: currentView.title,
                isActive: true,
              },
            ]}
          />

          {/* Content Area */}
          <ContentPlaceholder
            title={currentView.title}
            content={currentView.content}
            icon={appData.icon}
            appColor={appData.color}
            onGetStarted={() => console.log('Get started clicked')}
          />
        </div>
      </AppDynamicContent>
    </div>
  );
};

export default DynamicAppView;

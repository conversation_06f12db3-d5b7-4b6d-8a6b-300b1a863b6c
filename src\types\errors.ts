// Error Types
// Comprehensive error type definitions and classification system

// Error Type Classification
export const ErrorType = {
  // Network and API errors
  NETWORK: 'NETWORK',
  API: 'API',
  TIMEOUT: 'TIMEOUT',
  
  // Application errors
  RUNTIME: 'RUNTIME',
  BOUNDARY: 'BOUNDARY',
  CHUNK_LOAD: 'CHUNK_LOAD',
  
  // User and validation errors
  VALIDATION: 'VALIDATION',
  AUTHENTICATION: 'AUTHENTICATION',
  AUTHORIZATION: 'AUTHORIZATION',
  
  // System errors
  CONFIGURATION: 'CONFIGURATION',
  STORAGE: 'STORAGE',
  UNKNOWN: 'UNKNOWN',
} as const;

export type ErrorType = typeof ErrorType[keyof typeof ErrorType];

// Error Severity Levels
export const ErrorSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL',
} as const;

export type ErrorSeverity = typeof ErrorSeverity[keyof typeof ErrorSeverity];

// Recovery Actions
export const RecoveryAction = {
  RETRY: 'RETRY',
  RELOAD: 'RELOAD',
  REDIRECT: 'REDIRECT',
  LOGOUT: 'LOGOUT',
  CONTACT_SUPPORT: 'CONTACT_SUPPORT',
  IGNORE: 'IGNORE',
  FALLBACK: 'FALLBACK',
} as const;

export type RecoveryAction = typeof RecoveryAction[keyof typeof RecoveryAction];

// Base Error Interface
export interface AppError extends Error {
  code?: string;
  type: ErrorType;
  severity: ErrorSeverity;
  context?: ErrorContext;
  timestamp: Date;
  userMessage?: string;
  technicalMessage?: string;
  recoverable: boolean;
  retryable: boolean;
  reportable: boolean;
  stack?: string;
  cause?: Error;
}

// Error Context for Better Debugging
export interface ErrorContext {
  component?: string;
  route?: string;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  timestamp: Date;
  buildVersion?: string;
  environment?: string;
  errorBoundaryVersion?: string;
  viewport?: {
    width: number;
    height: number;
  };
  synchronousError?: boolean;
  globalError?: boolean;
  additionalData?: Record<string, any>;
}

// Error Classification Configuration
export interface ErrorClassification {
  type: ErrorType;
  severity: ErrorSeverity;
  userMessage: string;
  technicalMessage?: string;
  recoverable: boolean;
  retryable: boolean;
  reportable: boolean;
  suggestedActions: RecoveryAction[];
  autoRetryCount?: number;
  autoRetryDelay?: number;
}

// Network Error
export interface NetworkError extends AppError {
  type: typeof ErrorType.NETWORK;
  statusCode?: number;
  responseData?: any;
  requestConfig?: any;
}

// API Error
export interface ApiError extends AppError {
  type: typeof ErrorType.API;
  statusCode: number;
  endpoint: string;
  method: string;
  responseData?: any;
  requestData?: any;
}

// Validation Error
export interface ValidationError extends AppError {
  type: typeof ErrorType.VALIDATION;
  field?: string;
  value?: any;
  constraints?: string[];
  validationRules?: ValidationRule[];
}

// Validation Rule
export interface ValidationRule {
  rule: string;
  message: string;
  params?: any;
}

// Authentication Error
export interface AuthenticationError extends AppError {
  type: typeof ErrorType.AUTHENTICATION;
  reason: 'invalid_credentials' | 'token_expired' | 'token_invalid' | 'session_expired' | 'mfa_required';
  redirectUrl?: string;
}

// Authorization Error
export interface AuthorizationError extends AppError {
  type: typeof ErrorType.AUTHORIZATION;
  resource: string;
  action: string;
  requiredPermissions: string[];
  userPermissions: string[];
}

// Runtime Error
export interface RuntimeError extends AppError {
  type: typeof ErrorType.RUNTIME;
  componentStack?: string;
  errorInfo?: any;
}

// Configuration Error
export interface ConfigurationError extends AppError {
  type: typeof ErrorType.CONFIGURATION;
  configKey: string;
  expectedType?: string;
  actualValue?: any;
}

// Storage Error
export interface StorageError extends AppError {
  type: typeof ErrorType.STORAGE;
  storageType: 'localStorage' | 'sessionStorage' | 'indexedDB' | 'webSQL' | 'cookies';
  operation: 'read' | 'write' | 'delete' | 'clear';
  key?: string;
}

// Error Boundary State
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: AppError;
  errorInfo?: any;
  retryCount: number;
  lastRetryAt?: Date;
}

// Error Boundary Props
export interface ErrorBoundaryProps {
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: AppError, errorInfo: any) => void;
  onRetry?: () => void;
  maxRetries?: number;
  retryDelay?: number;
  isolate?: boolean;
  children: React.ReactNode;
}

// Error Fallback Props
export interface ErrorFallbackProps {
  error: AppError;
  retry: () => void;
  canRetry: boolean;
  retryCount: number;
  maxRetries: number;
}

// Error Reporter Configuration
export interface ErrorReporterConfig {
  enabled: boolean;
  endpoint?: string;
  apiKey?: string;
  environment: string;
  userId?: string;
  sessionId?: string;
  tags?: Record<string, string>;
  beforeSend?: (error: AppError) => AppError | null;
  onError?: (error: Error) => void;
  enableConsoleLogging: boolean;
  enableRemoteLogging: boolean;
  enableLocalStorage: boolean;
  maxLocalErrors: number;
  batchSize: number;
  flushInterval: number;
}

// Error Report
export interface ErrorReport {
  id: string;
  error: AppError;
  context: ErrorContext;
  timestamp: Date;
  reported: boolean;
  reportedAt?: Date;
  attempts: number;
  lastAttemptAt?: Date;
}

// Error Statistics
export interface ErrorStatistics {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByComponent: Record<string, number>;
  errorsByRoute: Record<string, number>;
  recentErrors: ErrorReport[];
  topErrors: {
    error: AppError;
    count: number;
    lastOccurrence: Date;
  }[];
}

// Error Handler Options
export interface ErrorHandlerOptions {
  showUserMessage: boolean;
  showTechnicalDetails: boolean;
  enableRetry: boolean;
  enableReporting: boolean;
  autoRetry: boolean;
  maxAutoRetries: number;
  retryDelay: number;
  fallbackComponent?: React.ComponentType<any>;
  onError?: (error: AppError) => void;
  onRetry?: (error: AppError) => void;
  onRecover?: (error: AppError) => void;
}

// Global Error Handler
export interface GlobalErrorHandler {
  handleError: (error: Error | AppError, context?: Partial<ErrorContext>) => void;
  handlePromiseRejection: (event: PromiseRejectionEvent) => void;
  handleResourceError: (event: Event) => void;
  reportError: (error: AppError) => Promise<void>;
  clearErrors: () => void;
  getErrorStatistics: () => ErrorStatistics;
  subscribe: (callback: (error: AppError) => void) => () => void;
}

// Error Recovery Strategy
export interface ErrorRecoveryStrategy {
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<boolean>;
  fallback: (error: AppError) => React.ComponentType<any> | null;
  retry: (error: AppError) => Promise<boolean>;
  shouldRetry: (error: AppError, retryCount: number) => boolean;
  getRetryDelay: (error: AppError, retryCount: number) => number;
}

// Predefined Error Classifications
export const ERROR_CLASSIFICATIONS: Record<string, ErrorClassification> = {
  NETWORK_OFFLINE: {
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.HIGH,
    userMessage: 'You appear to be offline. Please check your internet connection.',
    recoverable: true,
    retryable: true,
    reportable: false,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.RELOAD],
    autoRetryCount: 3,
    autoRetryDelay: 2000,
  },
  NETWORK_TIMEOUT: {
    type: ErrorType.TIMEOUT,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'The request is taking longer than expected. Please try again.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.RELOAD],
    autoRetryCount: 2,
    autoRetryDelay: 3000,
  },
  API_SERVER_ERROR: {
    type: ErrorType.API,
    severity: ErrorSeverity.HIGH,
    userMessage: 'A server error occurred. Please try again later.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.CONTACT_SUPPORT],
    autoRetryCount: 1,
    autoRetryDelay: 5000,
  },
  AUTHENTICATION_FAILED: {
    type: ErrorType.AUTHENTICATION,
    severity: ErrorSeverity.HIGH,
    userMessage: 'Authentication failed. Please log in again.',
    recoverable: true,
    retryable: false,
    reportable: false,
    suggestedActions: [RecoveryAction.LOGOUT, RecoveryAction.REDIRECT],
  },
  AUTHORIZATION_DENIED: {
    type: ErrorType.AUTHORIZATION,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'You do not have permission to perform this action.',
    recoverable: false,
    retryable: false,
    reportable: false,
    suggestedActions: [RecoveryAction.CONTACT_SUPPORT],
  },
  VALIDATION_FAILED: {
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.LOW,
    userMessage: 'Please check your input and try again.',
    recoverable: true,
    retryable: false,
    reportable: false,
    suggestedActions: [RecoveryAction.IGNORE],
  },
  RUNTIME_ERROR: {
    type: ErrorType.RUNTIME,
    severity: ErrorSeverity.HIGH,
    userMessage: 'An unexpected error occurred. Please refresh the page.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RELOAD, RecoveryAction.CONTACT_SUPPORT],
    autoRetryCount: 1,
    autoRetryDelay: 1000,
  },
  CHUNK_LOAD_ERROR: {
    type: ErrorType.CHUNK_LOAD,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'Failed to load application resources. Please refresh the page.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RELOAD],
    autoRetryCount: 2,
    autoRetryDelay: 1000,
  },
};

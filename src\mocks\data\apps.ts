// Application mock data for MSW
export interface AppData {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  gradient?: boolean;
  category?: string;
  isActive?: boolean;
  version?: string;
  createdAt?: string;
  updatedAt?: string;
  permissions?: string[];
  navLinks?: Array<{
    label: string;
    href: string;
    isActive?: boolean;
    icon?: string;
    permissions?: string[];
  }>;
  views?: Record<
    string,
    {
      title: string;
      content: string;
      permissions?: string[];
    }
  >;
  settings?: {
    enableNotifications?: boolean;
    enableAnalytics?: boolean;
    maxUsers?: number;
    features?: string[];
  };
}

export const apps: AppData[] = [
  {
    id: '1',
    title: 'Sales',
    description: 'Manage sales orders and customers',
    icon: '💼',
    color: '#3b82f6', // blue-500
    gradient: true,
    category: 'Business',
    isActive: true,
    version: '2.1.0',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    permissions: ['sales:read', 'sales:write'],
    settings: {
      enableNotifications: true,
      enableAnalytics: true,
      maxUsers: 100,
      features: ['dashboard', 'orders', 'customers', 'reports'],
    },
    navLinks: [
      {
        label: 'Dashboard',
        href: '/app?menu=1&view=dashboard',
        isActive: true,
        icon: '📊',
        permissions: ['sales:read'],
      },
      {
        label: 'Orders',
        href: '/app?menu=1&view=orders',
        isActive: false,
        icon: '📋',
        permissions: ['sales:read'],
      },
      {
        label: 'Customers',
        href: '/app?menu=1&view=customers',
        isActive: false,
        icon: '👥',
        permissions: ['sales:read'],
      },
      {
        label: 'Products',
        href: '/app?menu=1&view=products',
        isActive: false,
        icon: '📦',
        permissions: ['sales:read'],
      },
      {
        label: 'Reports',
        href: '/app?menu=1&view=reports',
        isActive: false,
        icon: '📈',
        permissions: ['sales:read', 'reports:read'],
      },
    ],
    views: {
      dashboard: {
        title: 'Sales Dashboard',
        content:
          'Welcome to the Sales Dashboard. Here you can view your sales metrics and performance.',
        permissions: ['sales:read'],
      },
      orders: {
        title: 'Sales Orders',
        content: 'Manage your sales orders and track their status.',
        permissions: ['sales:read'],
      },
      customers: {
        title: 'Customer Management',
        content: 'View and manage your customer database.',
        permissions: ['sales:read'],
      },
      products: {
        title: 'Product Catalog',
        content: 'Manage your product catalog and pricing.',
        permissions: ['sales:read', 'products:read'],
      },
      reports: {
        title: 'Sales Reports',
        content: 'Generate and view sales reports and analytics.',
        permissions: ['sales:read', 'reports:read'],
      },
    },
  },
  {
    id: '2',
    title: 'Inventory',
    description: 'Track products and stock levels',
    icon: '📦',
    color: '#10b981', // emerald-500
    gradient: true,
    category: 'Operations',
    isActive: true,
    version: '1.8.2',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-16T14:20:00Z',
    permissions: ['inventory:read', 'inventory:write'],
    settings: {
      enableNotifications: true,
      enableAnalytics: false,
      maxUsers: 50,
      features: ['tracking', 'alerts', 'reports'],
    },
  },
  {
    id: '3',
    title: 'Finance',
    description: 'Financial management and reporting',
    icon: '💰',
    color: '#f59e0b', // amber-500
    gradient: true,
    category: 'Finance',
    isActive: true,
    version: '3.0.1',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
    permissions: ['finance:read', 'finance:write', 'admin'],
    settings: {
      enableNotifications: true,
      enableAnalytics: true,
      maxUsers: 25,
      features: ['accounting', 'budgeting', 'reporting', 'compliance'],
    },
  },
  {
    id: '4',
    title: 'HR',
    description: 'Human resources and employee management',
    icon: '👥',
    color: '#8b5cf6', // violet-500
    gradient: true,
    category: 'Human Resources',
    isActive: true,
    version: '2.5.0',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-18T11:45:00Z',
    permissions: ['hr:read', 'hr:write'],
    settings: {
      enableNotifications: true,
      enableAnalytics: true,
      maxUsers: 75,
      features: ['employees', 'payroll', 'benefits', 'performance'],
    },
  },
  {
    id: '5',
    title: 'Projects',
    description: 'Project management and collaboration',
    icon: '🎯',
    color: '#06b6d4', // cyan-500
    gradient: true,
    category: 'Productivity',
    isActive: true,
    version: '1.9.3',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-19T08:30:00Z',
    permissions: ['projects:read', 'projects:write'],
    settings: {
      enableNotifications: true,
      enableAnalytics: false,
      maxUsers: 200,
      features: ['tasks', 'timeline', 'collaboration', 'reporting'],
    },
  },
];

// Helper functions
export const getAppById = (id: string): AppData | undefined => {
  return apps.find(app => app.id === id);
};

export const getActiveApps = (): AppData[] => {
  return apps.filter(app => app.isActive !== false);
};

export const getAppsByCategory = (category: string): AppData[] => {
  return apps.filter(app => app.category === category);
};

export const getAppsMap = (): Record<string, AppData> => {
  return apps.reduce(
    (acc, app) => {
      acc[app.id] = app;
      return acc;
    },
    {} as Record<string, AppData>
  );
};

export const searchApps = (query: string): AppData[] => {
  const lowercaseQuery = query.toLowerCase();
  return apps.filter(
    app =>
      app.title.toLowerCase().includes(lowercaseQuery) ||
      app.description.toLowerCase().includes(lowercaseQuery) ||
      app.category?.toLowerCase().includes(lowercaseQuery)
  );
};

export const getAppCategories = (): string[] => {
  const categories = apps
    .map(app => app.category)
    .filter((category): category is string => Boolean(category));
  return [...new Set(categories)];
};

import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface TextProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  variant?:
    | 'body'
    | 'caption'
    | 'overline'
    | 'subtitle1'
    | 'subtitle2'
    | 'label'
    | 'helper';
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  color?:
    | 'primary'
    | 'secondary'
    | 'muted'
    | 'error'
    | 'warning'
    | 'success'
    | 'info'
    | 'inherit'
    | 'inverse';
  align?: 'left' | 'center' | 'right' | 'justify';
  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  truncate?: boolean;
  lineClamp?: 1 | 2 | 3 | 4 | 5;
  as?: 'p' | 'span' | 'div' | 'label' | 'small' | 'strong' | 'em';
  'data-testid'?: string;
}

/**
 * Global Text component - moved from ui/Text
 * Provides consistent text styling across the application
 */
export const Text: React.FC<TextProps> = ({
  children,
  variant = 'body',
  size,
  weight = 'normal',
  color = 'inherit',
  align = 'left',
  transform = 'none',
  truncate = false,
  lineClamp,
  as: Component = 'p',
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  // Variant-based defaults
  const variantDefaults = {
    body: { size: 'base' as const, weight: 'normal' as const },
    caption: { size: 'sm' as const, weight: 'normal' as const },
    overline: { size: 'xs' as const, weight: 'medium' as const },
    subtitle1: { size: 'lg' as const, weight: 'medium' as const },
    subtitle2: { size: 'base' as const, weight: 'medium' as const },
    label: { size: 'sm' as const, weight: 'medium' as const },
    helper: { size: 'xs' as const, weight: 'normal' as const },
  };

  const finalSize = size || variantDefaults[variant].size;
  const finalWeight = weight || variantDefaults[variant].weight;

  // Size classes
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
  };

  // Weight classes
  const weightClasses = {
    light: 'font-light',
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  };

  // Alignment classes
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  // Transform classes
  const transformClasses = {
    none: '',
    uppercase: 'uppercase',
    lowercase: 'lowercase',
    capitalize: 'capitalize',
  };

  // Color mapping with theme-aware colors
  const getTextColor = () => {
    switch (color) {
      case 'primary':
        return colors.primary;
      case 'secondary':
        return colors.textSecondary;
      case 'muted':
        return colors.mutedForeground;
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      case 'success':
        return colors.success;
      case 'info':
        return colors.info;
      case 'inverse':
        return colors.background;
      case 'inherit':
      default:
        return colors.text;
    }
  };

  // Build classes
  const classes = cn(
    sizeClasses[finalSize],
    weightClasses[finalWeight],
    alignClasses[align],
    transformClasses[transform],
    truncate && 'truncate',
    lineClamp && `line-clamp-${lineClamp}`,
    variant === 'overline' && 'tracking-wider',
    variant === 'label' && 'select-none',
    variant === 'helper' && 'leading-relaxed',
    className
  );

  const style = {
    color: getTextColor(),
    ...props.style,
  };

  return (
    <Component
      className={classes}
      style={style}
      data-testid={testId}
      {...props}
    >
      {children}
    </Component>
  );
};

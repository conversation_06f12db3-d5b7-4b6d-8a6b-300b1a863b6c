/**
 * Enhanced ErrorBoundary Component
 * Modern React error boundary with comprehensive error handling, reporting, recovery features,
 * performance optimizations, accessibility improvements, and advanced monitoring capabilities
 */

import React, { Component, Suspense, lazy } from 'react';
import type { ReactNode, ErrorInfo } from 'react';
import type { AppError } from '../../../utils/errorTypes';
import { createAppError } from '../../../utils/errorTypes';
import { reportError } from '../../../utils/errorReporting';
import { executeAutoRecovery } from '../../../utils/errorRecovery';
import { useThemeStore } from '../../../stores/themeStore';

// Lazy load ErrorFallback for better performance
const ErrorFallback = lazy(() => import('../ErrorFallback/ErrorFallback'));

// Performance monitoring hook
const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = React.useState({
    renderTime: 0,
    errorCatchTime: 0,
    recoveryTime: 0,
  });

  const startTimer = React.useCallback((type: keyof typeof metrics) => {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      setMetrics(prev => ({
        ...prev,
        [type]: endTime - startTime,
      }));
    };
  }, []);

  return { metrics, startTimer };
};

// Theme-aware recovery loading component with accessibility
const RecoveryLoading: React.FC<{ 
  message?: string;
  progress?: number;
  onCancel?: () => void;
}> = ({ 
  message = 'Attempting to recover...', 
  progress,
  onCancel 
}) => {
  const { colors } = useThemeStore();

  return (
    <div 
      className="flex items-center justify-center p-8"
      role="status"
      aria-live="polite"
      aria-label="Error recovery in progress"
    >
      <div className="text-center max-w-md">
        {/* Animated spinner with progress indicator */}
        <div className="relative mb-4">
          <div
            className="animate-spin rounded-full h-12 w-12 border-4 border-opacity-25 mx-auto"
            style={{ 
              borderColor: colors.primary,
              borderTopColor: 'transparent'
            }}
            aria-hidden="true"
          />
          {progress !== undefined && (
            <div 
              className="absolute inset-0 flex items-center justify-center text-xs font-semibold"
              style={{ color: colors.primary }}
            >
              {Math.round(progress)}%
            </div>
          )}
        </div>

        <p 
          className="text-sm mb-4"
          style={{ color: colors.textSecondary }}
        >
          {message}
        </p>

        {/* Progress bar */}
        {progress !== undefined && (
          <div 
            className="w-full bg-gray-200 rounded-full h-2 mb-4"
            role="progressbar"
            aria-valuenow={progress}
            aria-valuemin={0}
            aria-valuemax={100}
          >
            <div
              className="h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${progress}%`,
                backgroundColor: colors.primary 
              }}
            />
          </div>
        )}

        {/* Cancel button for long-running recoveries */}
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-sm underline transition-colors duration-200 hover:opacity-80"
            style={{ color: colors.textSecondary }}
            aria-label="Cancel recovery process"
          >
            Cancel Recovery
          </button>
        )}
      </div>
    </div>
  );
};

// Enhanced props interface with new features
export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: AppError, errorInfo: ErrorInfo) => void;
  onRecovery?: (error: AppError, recoveryMethod: string) => void;
  enableAutoRecovery?: boolean;
  enableReporting?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableAccessibilityFeatures?: boolean;
  isolateErrors?: boolean;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  level?: 'page' | 'section' | 'component';
  componentName?: string;
  maxRetryAttempts?: number;
  retryDelay?: number;
  gracefulDegradation?: boolean;
  fallbackTimeout?: number;
  enableErrorBoundaryChaining?: boolean;
  customRecoveryStrategies?: Array<{
    name: string;
    condition: (error: AppError) => boolean;
    handler: (error: AppError) => Promise<boolean>;
  }>;
  'data-testid'?: string;
  'aria-label'?: string;
}

// Enhanced props for fallback component
export interface ErrorFallbackProps {
  error: AppError;
  resetError: () => void;
  componentName?: string;
  level?: 'page' | 'section' | 'component';
  performanceMetrics?: {
    renderTime: number;
    errorCatchTime: number;
    recoveryTime: number;
  };
  retryCount?: number;
  onFeedback?: (feedback: string) => void;
}

// Enhanced state interface with new tracking capabilities
interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  errorId: string | null;
  retryCount: number;
  isRecovering: boolean;
  recoveryProgress: number;
  lastErrorTimestamp: number | null;
  errorHistory: Array<{
    error: AppError;
    timestamp: number;
    resolved: boolean;
  }>;
  performanceImpact: {
    errorFrequency: number;
    averageRecoveryTime: number;
    successfulRecoveries: number;
  };
}

/**
 * Enhanced React Error Boundary with comprehensive error handling and modern features
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;
  private autoRecoveryTimeoutId: number | null = null;
  private recoveryProgressInterval: number | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  private errorThrottleMap: Map<string, number> = new Map();
  private abortController: AbortController | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorId: null,
      retryCount: 0,
      isRecovering: false,
      recoveryProgress: 0,
      lastErrorTimestamp: null,
      errorHistory: [],
      performanceImpact: {
        errorFrequency: 0,
        averageRecoveryTime: 0,
        successfulRecoveries: 0,
      },
    };

    // Initialize performance monitoring
    if (props.enablePerformanceMonitoring && 'PerformanceObserver' in window) {
      this.initializePerformanceMonitoring();
    }
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('error-boundary')) {
            console.debug('Error Boundary Performance:', entry);
          }
        });
      });

      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'navigation', 'resource'] 
      });
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }

  /**
   * Enhanced error derivation with throttling and deduplication
   */
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const now = Date.now();
    const appError = createAppError(error, undefined, {
      component: 'ErrorBoundary',
      timestamp: new Date(),
      errorBoundaryVersion: '2.0',
    });

    return {
      hasError: true,
      error: appError,
      errorId: `error_${now}_${Math.random().toString(36).substring(2, 11)}`,
      lastErrorTimestamp: now,
    };
  }

  /**
   * Enhanced error handling with comprehensive logging and recovery
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    const startTime = performance.now();
    
    const {
      onError,
      enableReporting = true,
      enableAutoRecovery = true,
      componentName,
      maxRetryAttempts = 3,
    } = this.props;

    // Check for error throttling
    const errorSignature = `${error.name}-${error.message}`;
    const lastErrorTime = this.errorThrottleMap.get(errorSignature) || 0;
    const now = Date.now();
    
    if (now - lastErrorTime < 1000) { // Throttle identical errors within 1 second
      console.warn('Error throttled:', errorSignature);
      return;
    }
    
    this.errorThrottleMap.set(errorSignature, now);

    // Enhanced error object creation
    const appError = this.state.error || createAppError(error, undefined, {
      component: componentName || 'Unknown',
      route: window.location.pathname,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      additionalData: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        level: this.props.level || 'component',
        retryCount: this.state.retryCount,
        errorHistory: this.state.errorHistory.length,
        performanceImpact: this.state.performanceImpact,
      },
    });

    // Update error history
    this.setState(prevState => ({
      errorHistory: [
        ...prevState.errorHistory.slice(-9), // Keep last 10 errors
        {
          error: appError,
          timestamp: now,
          resolved: false,
        },
      ],
      performanceImpact: {
        ...prevState.performanceImpact,
        errorFrequency: prevState.performanceImpact.errorFrequency + 1,
      },
    }));

    // Call custom error handler
    if (onError) {
      try {
        onError(appError, errorInfo);
      } catch (handlerError) {
        console.error('Error in custom error handler:', handlerError);
      }
    }

    // Report error with enhanced context
    if (enableReporting) {
      this.reportError(appError, {
        ...errorInfo,
        performanceMetrics: {
          errorCatchTime: performance.now() - startTime,
          memoryUsage: (performance as any).memory ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          } : undefined,
        },
      });
    }

    // Attempt auto recovery with enhanced logic
    if (enableAutoRecovery && 
        !this.state.isRecovering && 
        this.state.retryCount < maxRetryAttempts) {
      this.attemptEnhancedAutoRecovery(appError);
    }

    // Accessibility announcement
    if (this.props.enableAccessibilityFeatures) {
      this.announceErrorToScreenReader(appError);
    }
  }

  /**
   * Enhanced component update logic with performance tracking
   */
  componentDidUpdate(prevProps: ErrorBoundaryProps, prevState: ErrorBoundaryState): void {
    const { resetOnPropsChange, resetKeys, onRecovery } = this.props;
    const { hasError, error } = this.state;

    // Handle reset on props change
    if (hasError && resetOnPropsChange && resetKeys && prevProps.resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== prevProps.resetKeys![index]
      );

      if (hasResetKeyChanged) {
        this.resetError('props-change');
      }
    }

    // Track successful recovery
    if (prevState.hasError && !hasError && prevState.error && onRecovery) {
      onRecovery(prevState.error, 'auto-recovery');
      
      // Update performance metrics
      this.setState(prevState => ({
        performanceImpact: {
          ...prevState.performanceImpact,
          successfulRecoveries: prevState.performanceImpact.successfulRecoveries + 1,
        },
      }));
    }
  }

  /**
   * Enhanced cleanup with comprehensive resource management
   */
  componentWillUnmount(): void {
    this.clearTimeouts();
    this.abortController?.abort();
    this.performanceObserver?.disconnect();
    this.errorThrottleMap.clear();
  }

  /**
   * Enhanced error reporting with comprehensive context
   */
  private async reportError(
    error: AppError,
    errorInfo: ErrorInfo & { performanceMetrics?: any }
  ): Promise<void> {
    try {
      await reportError(error, {
        componentStack: errorInfo.componentStack,
        errorBoundaryLevel: this.props.level,
        componentName: this.props.componentName,
        retryCount: this.state.retryCount,
        errorHistory: this.state.errorHistory,
        performanceMetrics: errorInfo.performanceMetrics,
        userInteraction: {
          lastClick: this.getLastUserInteraction(),
          scrollPosition: { x: window.scrollX, y: window.scrollY },
        },
        environmentInfo: {
          connectionType: (navigator as any).connection?.effectiveType,
          deviceMemory: (navigator as any).deviceMemory,
          hardwareConcurrency: navigator.hardwareConcurrency,
        },
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Enhanced auto recovery with progress tracking and custom strategies
   */
  private async attemptEnhancedAutoRecovery(error: AppError): Promise<void> {
    const { componentName, customRecoveryStrategies, retryDelay = 1000 } = this.props;
    const { retryCount } = this.state;

    this.setState({ 
      isRecovering: true, 
      recoveryProgress: 0 
    });

    // Start progress tracking
    this.startRecoveryProgress();

    try {
      // Try custom recovery strategies first
      if (customRecoveryStrategies) {
        for (const strategy of customRecoveryStrategies) {
          if (strategy.condition(error)) {
            const recovered = await strategy.handler(error);
            if (recovered) {
              this.completeRecovery('custom-strategy', strategy.name);
              return;
            }
          }
        }
      }

      // Enhanced recovery context
      const recoveryContext = {
        error,
        component: componentName,
        route: window.location.pathname,
        retryCount,
        userAgent: navigator.userAgent,
        timestamp: new Date(),
        performanceMetrics: this.state.performanceImpact,
      };

      // Calculate adaptive delay based on error frequency
      const adaptiveDelay = this.calculateAdaptiveDelay(retryDelay, retryCount);
      await new Promise(resolve => setTimeout(resolve, adaptiveDelay));

      this.setState({ recoveryProgress: 50 });

      const recovered = await executeAutoRecovery(recoveryContext);

      this.setState({ recoveryProgress: 100 });

      if (recovered) {
        this.completeRecovery('auto-recovery');
      } else {
        this.setState({ 
          isRecovering: false, 
          recoveryProgress: 0 
        });
      }
    } catch (recoveryError) {
      console.error('Enhanced auto recovery failed:', recoveryError);
      this.setState({ 
        isRecovering: false, 
        recoveryProgress: 0 
      });
    }
  }

  /**
   * Start recovery progress simulation
   */
  private startRecoveryProgress(): void {
    let progress = 0;
    this.recoveryProgressInterval = window.setInterval(() => {
      progress += Math.random() * 10;
      if (progress < 90) {
        this.setState({ recoveryProgress: Math.min(progress, 90) });
      }
    }, 200);
  }

  /**
   * Complete recovery process
   */
  private completeRecovery(method: string, strategyName?: string): void {
    this.setState({ recoveryProgress: 100 });
    
    setTimeout(() => {
      this.resetError(method);
      
      // Mark error as resolved in history
      this.setState(prevState => ({
        errorHistory: prevState.errorHistory.map(entry => 
          entry.error === prevState.error 
            ? { ...entry, resolved: true }
            : entry
        ),
      }));

      // Announce successful recovery
      if (this.props.enableAccessibilityFeatures) {
        this.announceRecoveryToScreenReader(method, strategyName);
      }
    }, 500);
  }

  /**
   * Calculate adaptive delay based on error patterns
   */
  private calculateAdaptiveDelay(baseDelay: number, retryCount: number): number {
    const { errorFrequency } = this.state.performanceImpact;
    
    // Exponential backoff with frequency adjustment
    const exponentialDelay = baseDelay * Math.pow(2, retryCount);
    const frequencyMultiplier = Math.min(errorFrequency * 0.1, 2);
    
    return Math.min(exponentialDelay * (1 + frequencyMultiplier), 30000);
  }

  /**
   * Enhanced error reset with comprehensive cleanup
   */
  private resetError = (method: string = 'manual'): void => {
    this.clearTimeouts();
    this.abortController?.abort();
    this.abortController = new AbortController();

    const recoveryTime = this.state.lastErrorTimestamp 
      ? Date.now() - this.state.lastErrorTimestamp 
      : 0;

    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorId: null,
      retryCount: method === 'auto-recovery' ? prevState.retryCount + 1 : 0,
      isRecovering: false,
      recoveryProgress: 0,
      lastErrorTimestamp: null,
      performanceImpact: {
        ...prevState.performanceImpact,
        averageRecoveryTime: prevState.performanceImpact.averageRecoveryTime > 0
          ? (prevState.performanceImpact.averageRecoveryTime + recoveryTime) / 2
          : recoveryTime,
      },
    }));

    // Performance mark
    if (this.props.enablePerformanceMonitoring) {
      performance.mark(`error-boundary-reset-${method}`);
    }
  };

  /**
   * Clear all timeouts and intervals
   */
  private clearTimeouts(): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
    if (this.autoRecoveryTimeoutId) {
      clearTimeout(this.autoRecoveryTimeoutId);
      this.autoRecoveryTimeoutId = null;
    }
    if (this.recoveryProgressInterval) {
      clearInterval(this.recoveryProgressInterval);
      this.recoveryProgressInterval = null;
    }
  }

  /**
   * Accessibility: Announce error to screen readers
   */
  private announceErrorToScreenReader(error: AppError): void {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'assertive');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = `Error occurred: ${error.userMessage || error.message}. Recovery options are available.`;
    
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }

  /**
   * Accessibility: Announce successful recovery
   */
  private announceRecoveryToScreenReader(method: string, strategyName?: string): void {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = `Error recovered successfully using ${strategyName || method}.`;
    
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }

  /**
   * Get last user interaction for context
   */
  private getLastUserInteraction(): string | null {
    // This would typically be tracked by a global event listener
    // For now, return null as placeholder
    return null;
  }

  /**
   * Enhanced render method with graceful degradation and performance optimization
   */
  render(): ReactNode {
    const { hasError, error, isRecovering, recoveryProgress } = this.state;
    const {
      children,
      fallback: FallbackComponent = ErrorFallback,
      isolateErrors,
      level,
      componentName,
      gracefulDegradation,
      fallbackTimeout = 5000,
      enablePerformanceMonitoring,
      'data-testid': testId,
      'aria-label': ariaLabel,
    } = this.props;

    // Performance marking
    if (enablePerformanceMonitoring) {
      performance.mark('error-boundary-render-start');
    }

    if (hasError && error) {
      // Show recovery indicator with enhanced features
      if (isRecovering) {
        return (
          <RecoveryLoading 
            message="Recovering from error..."
            progress={recoveryProgress}
            onCancel={() => this.setState({ isRecovering: false })}
          />
        );
      }

      // Render fallback UI with Suspense for lazy loading
      const fallbackElement = (
        <Suspense 
          fallback={
            <RecoveryLoading message="Loading error interface..." />
          }
        >
          <FallbackComponent
            error={error}
            resetError={this.resetError}
            componentName={componentName}
            level={level}
            performanceMetrics={{
              renderTime: 0, // Would be calculated in real implementation
              errorCatchTime: 0,
              recoveryTime: 0,
            }}
            retryCount={this.state.retryCount}
            onFeedback={(feedback) => {
              reportError(error, { userFeedback: feedback });
            }}
          />
        </Suspense>
      );

      // Apply timeout for fallback rendering if specified
      if (fallbackTimeout > 0) {
        setTimeout(() => {
          if (this.state.hasError) {
            console.warn('Fallback component timeout reached');
          }
        }, fallbackTimeout);
      }

      return (
        <div
          data-testid={testId}
          aria-label={ariaLabel || `Error boundary for ${componentName || 'component'}`}
          role="alert"
          aria-live="assertive"
        >
          {fallbackElement}
        </div>
      );
    }

    // Enhanced error isolation with try-catch for synchronous errors
    if (isolateErrors) {
      try {
        const result = children;
        
        // Performance marking
        if (enablePerformanceMonitoring) {
          performance.mark('error-boundary-render-end');
          performance.measure(
            'error-boundary-render',
            'error-boundary-render-start',
            'error-boundary-render-end'
          );
        }
        
        return result;
      } catch (renderError) {
        // Handle synchronous render errors
        const appError = createAppError(renderError, undefined, {
          component: componentName || 'Unknown',
          route: window.location.pathname,
          synchronousError: true,
        });

        return (
          <Suspense fallback={<RecoveryLoading />}>
            <FallbackComponent
              error={appError}
              resetError={this.resetError}
              componentName={componentName}
              level={level}
            />
          </Suspense>
        );
      }
    }

    // Graceful degradation fallback
    if (gracefulDegradation && this.state.errorHistory.length > 5) {
      return (
        <div className="p-4 text-center">
          <p>This component is temporarily unavailable due to repeated errors.</p>
          <button 
            onClick={() => this.resetError('graceful-reset')}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      );
    }

    return (
      <div
        data-testid={testId}
        aria-label={ariaLabel}
      >
        {children}
      </div>
    );
  }
}

export default ErrorBoundary;

import React, { useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import { useDropdownContext } from './DropdownBase';
import type { DropdownSectionProps } from './types';

/**
 * Dropdown section component for grouping related items
 */
export const DropdownSection: React.FC<DropdownSectionProps> = ({
  title,
  icon,
  children,
  className = '',
  collapsible = false,
  defaultCollapsed = false,
}) => {
  const { colors } = useThemeStore();
  const { size } = useDropdownContext();
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          header: 'px-2 py-1',
          content: 'px-1',
          text: 'text-xs',
          icon: 'w-3 h-3',
        };
      case 'large':
        return {
          header: 'px-4 py-3',
          content: 'px-2',
          text: 'text-base',
          icon: 'w-6 h-6',
        };
      default: // medium
        return {
          header: 'px-3 py-2',
          content: 'px-1',
          text: 'text-sm',
          icon: 'w-4 h-4',
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const handleToggle = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  return (
    <div className={cn('', className)}>
      {title && (
        <div
          className={cn(
            'flex items-center justify-between border-b mb-1',
            sizeClasses.header,
            collapsible && 'cursor-pointer hover:bg-opacity-50'
          )}
          style={{
            borderColor: colors.border,
            backgroundColor: collapsible ? 'transparent' : undefined,
          }}
          onClick={handleToggle}
          role={collapsible ? 'button' : undefined}
          aria-expanded={collapsible ? !isCollapsed : undefined}
          onMouseEnter={(e) => {
            if (collapsible) {
              e.currentTarget.style.backgroundColor = `${colors.hover}10`;
            }
          }}
          onMouseLeave={(e) => {
            if (collapsible) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          <div className="flex items-center space-x-2">
            {icon && (
              <div
                className={cn('flex-shrink-0', sizeClasses.icon)}
                style={{ color: colors.primary }}
              >
                {icon}
              </div>
            )}
            <h3
              className={cn('font-semibold', sizeClasses.text)}
              style={{ color: colors.text }}
            >
              {title}
            </h3>
          </div>
          
          {collapsible && (
            <div
              className={cn(
                'transition-transform duration-200',
                isCollapsed ? 'rotate-0' : 'rotate-180'
              )}
              style={{ color: colors.mutedForeground }}
            >
              <svg className={sizeClasses.icon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          )}
        </div>
      )}
      
      {(!collapsible || !isCollapsed) && (
        <div className={cn('space-y-1', sizeClasses.content)}>
          {children}
        </div>
      )}
    </div>
  );
};

// User Management Helper Functions
import type { User, UserRole, UserStatus } from '../types';

/**
 * Generate user avatar initials
 */
export const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

/**
 * Get user display name
 */
export const getUserDisplayName = (user: User): string => {
  return user.name || user.email.split('@')[0];
};

/**
 * Get role display name
 */
export const getRoleDisplayName = (role: UserRole): string => {
  const roleNames: Record<UserRole, string> = {
    admin: 'Administrator',
    moderator: 'Moderator',
    user: 'User',
    viewer: 'Viewer',
  };
  return roleNames[role] || role;
};

/**
 * Get status display name
 */
export const getStatusDisplayName = (status: UserStatus): string => {
  const statusNames: Record<UserStatus, string> = {
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    suspended: 'Suspended',
  };
  return statusNames[status] || status;
};

/**
 * Get role color for UI display
 */
export const getRoleColor = (role: UserRole): string => {
  const roleColors: Record<UserRole, string> = {
    admin: '#ef4444', // red
    moderator: '#f59e0b', // amber
    user: '#3b82f6', // blue
    viewer: '#6b7280', // gray
  };
  return roleColors[role] || '#6b7280';
};

/**
 * Get status color for UI display
 */
export const getStatusColor = (status: UserStatus): string => {
  const statusColors: Record<UserStatus, string> = {
    active: '#10b981', // green
    inactive: '#6b7280', // gray
    pending: '#f59e0b', // amber
    suspended: '#ef4444', // red
  };
  return statusColors[status] || '#6b7280';
};

/**
 * Sort users by various criteria
 */
export const sortUsers = (users: User[], sortBy: 'name' | 'email' | 'role' | 'status' | 'createdAt', direction: 'asc' | 'desc' = 'asc'): User[] => {
  return [...users].sort((a, b) => {
    let aValue: string | Date;
    let bValue: string | Date;

    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'email':
        aValue = a.email.toLowerCase();
        bValue = b.email.toLowerCase();
        break;
      case 'role':
        aValue = a.role;
        bValue = b.role;
        break;
      case 'status':
        aValue = a.status || 'active';
        bValue = b.status || 'active';
        break;
      case 'createdAt':
        aValue = new Date(a.createdAt || '');
        bValue = new Date(b.createdAt || '');
        break;
      default:
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
    }

    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

/**
 * Filter users by search term
 */
export const filterUsersBySearch = (users: User[], searchTerm: string): User[] => {
  if (!searchTerm.trim()) return users;

  const term = searchTerm.toLowerCase();
  return users.filter(user =>
    user.name.toLowerCase().includes(term) ||
    user.email.toLowerCase().includes(term) ||
    user.role.toLowerCase().includes(term)
  );
};

/**
 * Filter users by role
 */
export const filterUsersByRole = (users: User[], roles: UserRole[]): User[] => {
  if (!roles.length) return users;
  return users.filter(user => roles.includes(user.role));
};

/**
 * Filter users by status
 */
export const filterUsersByStatus = (users: User[], statuses: UserStatus[]): User[] => {
  if (!statuses.length) return users;
  return users.filter(user => statuses.includes(user.status || 'active'));
};

/**
 * Check if user has specific permission
 */
export const userHasPermission = (user: User, permission: string): boolean => {
  if (!user.permissions) return false;
  return user.permissions.some(p => p.name === permission || p.name === 'admin');
};

/**
 * Get user's last activity description
 */
export const getUserLastActivity = (user: User): string => {
  if (!user.lastLoginAt) return 'Never logged in';
  
  const lastLogin = new Date(user.lastLoginAt);
  const now = new Date();
  const diffMs = now.getTime() - lastLogin.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  return `${Math.floor(diffDays / 30)} months ago`;
};

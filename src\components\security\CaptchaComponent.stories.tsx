import type { Meta, StoryObj } from '@storybook/react-vite';
import { CaptchaComponent } from './CaptchaComponent';

const meta: Meta<typeof CaptchaComponent> = {
  title: 'Security/CaptchaComponent',
  component: CaptchaComponent,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A math-based CAPTCHA component for bot protection with accessibility features and customizable themes.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onVerify: {
      action: 'verify',
      description:
        'Callback function called when CAPTCHA is successfully verified',
    },
    onExpire: {
      action: 'expire',
      description: 'Callback function called when CAPTCHA expires',
    },
    onError: {
      action: 'error',
      description: 'Callback function called when an error occurs',
    },
    theme: {
      control: { type: 'select' },
      options: ['light', 'dark', 'auto'],
      description: 'Theme for the CAPTCHA component',
    },
    size: {
      control: { type: 'select' },
      options: ['normal', 'compact'],
      description: 'Size variant of the component',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    theme: 'auto',
    size: 'normal',
  },
};

export const LightTheme: Story = {
  args: {
    theme: 'light',
    size: 'normal',
  },
};

export const DarkTheme: Story = {
  args: {
    theme: 'dark',
    size: 'normal',
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};

export const CompactSize: Story = {
  args: {
    theme: 'auto',
    size: 'compact',
  },
};

export const CompactDark: Story = {
  args: {
    theme: 'dark',
    size: 'compact',
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};

// Story showing the verification flow
export const VerificationFlow: Story = {
  render: () => {
    return (
      <div className="w-96 space-y-4">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Try the CAPTCHA:
          </h3>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Solve the math problem correctly</li>
            <li>• Try entering wrong answers (max 3 attempts)</li>
            <li>• Use the refresh button to get a new challenge</li>
            <li>• Test keyboard navigation (Tab, Enter)</li>
          </ul>
        </div>
        <CaptchaComponent />
      </div>
    );
  },
};

// Story showing accessibility features
export const AccessibilityDemo: Story = {
  render: () => {
    return (
      <div className="w-96 space-y-4">
        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
            Accessibility Features:
          </h3>
          <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
            <li>• Keyboard navigation support</li>
            <li>• Screen reader friendly</li>
            <li>• High contrast support</li>
            <li>• Clear error messages</li>
            <li>• Proper ARIA labels</li>
            <li>• Focus management</li>
          </ul>
        </div>
        <CaptchaComponent />
      </div>
    );
  },
};

// Story showing different sizes side by side
export const SizeComparison: Story = {
  render: () => {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Normal Size</h3>
          <CaptchaComponent size="normal" />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Compact Size</h3>
          <CaptchaComponent size="compact" />
        </div>
      </div>
    );
  },
};

// Story showing theme variations
export const ThemeVariations: Story = {
  render: () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Light Theme</h3>
          <CaptchaComponent theme="light" />
        </div>

        <div className="bg-slate-900 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 text-white">Dark Theme</h3>
          <CaptchaComponent theme="dark" />
        </div>
      </div>
    );
  },
};

// Story showing integration with forms
export const FormIntegration: Story = {
  render: () => {
    return (
      <div className="w-96 space-y-6">
        <h3 className="text-lg font-semibold">Login Form with CAPTCHA</h3>

        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input
              type="email"
              className="w-full px-3 py-2 border rounded-lg"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Password</label>
            <input
              type="password"
              className="w-full px-3 py-2 border rounded-lg"
              placeholder="Enter your password"
            />
          </div>

          <CaptchaComponent />

          <button
            type="submit"
            className="w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Sign In
          </button>
        </form>
      </div>
    );
  },
};

// Story showing error states
export const ErrorStates: Story = {
  render: () => {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Normal State</h3>
          <CaptchaComponent />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">After Wrong Answer</h3>
          <div className="w-96">
            <CaptchaComponent />
            <p className="text-sm text-red-600 mt-2">
              Incorrect answer. Please try again.
            </p>
          </div>
        </div>
      </div>
    );
  },
};

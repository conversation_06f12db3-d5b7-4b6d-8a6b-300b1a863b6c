import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface BreadcrumbItem {
  label: string;
  onClick?: () => void;
  isActive?: boolean;
}

export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: string;
  className?: string;
  'data-testid'?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  separator = '/',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      data-testid={testId}
    >
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {item.onClick ? (
            <button
              onClick={item.onClick}
              className="hover:underline"
              style={{ 
                color: item.isActive ? colors.text : colors.textSecondary 
              }}
            >
              {item.label}
            </button>
          ) : (
            <span 
              style={{ 
                color: item.isActive ? colors.text : colors.textSecondary 
              }}
            >
              {item.label}
            </span>
          )}
          {index < items.length - 1 && (
            <span style={{ color: colors.textSecondary }}>
              {separator}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumb;

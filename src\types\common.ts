// Common Utility Types
// Reusable type definitions used across the application

import type { ReactNode } from 'react';

// Generic ID type
export type ID = string | number;

// Generic timestamp
export type Timestamp = Date | string | number;

// Generic status
export type Status = 'active' | 'inactive' | 'pending' | 'error' | 'loading' | 'success';

// Generic priority
export type Priority = 'low' | 'medium' | 'high' | 'urgent' | 'critical';

// Generic size
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

// Generic variant
export type Variant = 'default' | 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost';

// Generic color
export type Color = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';

// Generic alignment
export type Alignment = 'left' | 'center' | 'right' | 'justify';

// Generic position
export type Position = 'top' | 'bottom' | 'left' | 'right' | 'center';

// Generic direction
export type Direction = 'horizontal' | 'vertical';

// Generic orientation
export type Orientation = 'portrait' | 'landscape';

// Generic sort order
export type SortOrder = 'asc' | 'desc';

// Generic loading state
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
  lastUpdated?: Timestamp;
}

// Generic pagination
export interface Pagination {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Generic sort configuration
export interface SortConfig {
  field: string;
  order: SortOrder;
  label?: string;
}

// Generic filter configuration
export interface FilterConfig {
  field: string;
  operator: FilterOperator;
  value: any;
  label?: string;
  type?: FilterType;
}

// Filter operators
export type FilterOperator = 
  | 'equals' 
  | 'not_equals' 
  | 'contains' 
  | 'not_contains' 
  | 'starts_with' 
  | 'ends_with'
  | 'greater_than' 
  | 'less_than' 
  | 'greater_than_or_equal' 
  | 'less_than_or_equal'
  | 'between' 
  | 'not_between'
  | 'in' 
  | 'not_in'
  | 'is_null' 
  | 'is_not_null'
  | 'is_empty' 
  | 'is_not_empty';

// Filter types
export type FilterType = 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect' | 'range';

// Generic search configuration
export interface SearchConfig {
  query: string;
  fields: string[];
  filters: FilterConfig[];
  sorts: SortConfig[];
  pagination: Pagination;
  options?: SearchOptions;
}

// Search options
export interface SearchOptions {
  caseSensitive?: boolean;
  exactMatch?: boolean;
  fuzzySearch?: boolean;
  highlightMatches?: boolean;
  includeScore?: boolean;
  maxResults?: number;
}

// Generic API response
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: ApiError[];
  meta?: ResponseMeta;
}

// API error
export interface ApiError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

// Response metadata
export interface ResponseMeta {
  timestamp: Timestamp;
  requestId: string;
  version: string;
  pagination?: Pagination;
  totalCount?: number;
  executionTime?: number;
}

// Generic form field
export interface FormField {
  name: string;
  label: string;
  type: FormFieldType;
  value: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  validation?: ValidationRule[];
  options?: FormFieldOption[];
  description?: string;
  tooltip?: string;
}

// Form field types
export type FormFieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'tel' 
  | 'url'
  | 'textarea' 
  | 'select' 
  | 'multiselect' 
  | 'checkbox' 
  | 'radio'
  | 'date' 
  | 'time' 
  | 'datetime' 
  | 'file' 
  | 'image'
  | 'color' 
  | 'range' 
  | 'hidden';

// Form field option
export interface FormFieldOption {
  value: any;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
  description?: string;
}

// Validation rule
export interface ValidationRule {
  type: ValidationType;
  value?: any;
  message: string;
  condition?: (value: any, formData: any) => boolean;
}

// Validation types
export type ValidationType = 
  | 'required' 
  | 'min' 
  | 'max' 
  | 'minLength' 
  | 'maxLength'
  | 'pattern' 
  | 'email' 
  | 'url' 
  | 'number' 
  | 'integer'
  | 'custom';

// Generic modal configuration
export interface ModalConfig {
  title: string;
  content: ReactNode;
  size?: Size;
  closable?: boolean;
  maskClosable?: boolean;
  keyboard?: boolean;
  centered?: boolean;
  footer?: ReactNode;
  actions?: ModalAction[];
}

// Modal action
export interface ModalAction {
  label: string;
  action: () => void | Promise<void>;
  variant?: Variant;
  disabled?: boolean;
  loading?: boolean;
}

// Generic toast notification
export interface ToastNotification {
  id: string;
  title?: string;
  message: string;
  type: Color;
  duration?: number;
  closable?: boolean;
  actions?: ToastAction[];
  position?: ToastPosition;
}

// Toast action
export interface ToastAction {
  label: string;
  action: () => void;
}

// Toast position
export type ToastPosition = 
  | 'top-left' 
  | 'top-center' 
  | 'top-right'
  | 'bottom-left' 
  | 'bottom-center' 
  | 'bottom-right';

// Generic dropdown option
export interface DropdownOption {
  value: any;
  label: string;
  icon?: ReactNode;
  disabled?: boolean;
  divider?: boolean;
  children?: DropdownOption[];
}

// Generic table column
export interface TableColumn {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number | string;
  sortable?: boolean;
  filterable?: boolean;
  resizable?: boolean;
  fixed?: 'left' | 'right';
  align?: Alignment;
  render?: (value: any, record: any, index: number) => ReactNode;
}

// Generic breadcrumb item
export interface BreadcrumbItem {
  title: string;
  href?: string;
  icon?: ReactNode;
  onClick?: () => void;
}

// Generic menu item
export interface MenuItem {
  key: string;
  label: string;
  icon?: ReactNode;
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
  children?: MenuItem[];
  badge?: string | number;
  shortcut?: string;
}

// Generic file information
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  url?: string;
  preview?: string;
  metadata?: Record<string, any>;
}

// Generic coordinate
export interface Coordinate {
  x: number;
  y: number;
}

// Generic dimension
export interface Dimension {
  width: number;
  height: number;
}

// Generic rectangle
export interface Rectangle extends Coordinate, Dimension {}

// Generic key-value pair
export interface KeyValuePair<K = string, V = any> {
  key: K;
  value: V;
}

// Generic tree node
export interface TreeNode<T = any> {
  id: ID;
  label: string;
  data?: T;
  children?: TreeNode<T>[];
  parent?: TreeNode<T>;
  expanded?: boolean;
  selected?: boolean;
  disabled?: boolean;
  icon?: ReactNode;
}

// Generic event handler
export type EventHandler<T = any> = (event: T) => void;

// Generic async function
export type AsyncFunction<T = any, R = any> = (args: T) => Promise<R>;

// Generic callback function
export type Callback<T = any, R = void> = (args: T) => R;

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type Required<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type Maybe<T> = T | null | undefined;

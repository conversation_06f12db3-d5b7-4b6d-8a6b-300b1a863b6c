import React from 'react';
import { useUsers } from '../../hooks';
import { UserCard } from '../UserCard';
import type { User } from '../../types';

export interface UserListProps {
  onUserEdit?: (user: User) => void;
  onUserDelete?: (user: User) => void;
  showActions?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const UserList: React.FC<UserListProps> = ({
  onUserEdit,
  onUserDelete,
  showActions = true,
  className = '',
  'data-testid': testId,
}) => {
  const { users, loading, error, refetch } = useUsers();

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8" data-testid="user-list-loading">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-slate-600 dark:text-slate-400">
          Loading users...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4" data-testid="user-list-error">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="text-red-400 mr-2">⚠️</div>
            <span className="text-red-800 dark:text-red-200">
              {error}
            </span>
          </div>
          <button
            onClick={refetch}
            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm"
            data-testid="user-list-retry"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`} data-testid={testId}>
      <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
        Users ({users.length})
      </h2>

      {users.length === 0 ? (
        <div className="text-center py-8 text-slate-500 dark:text-slate-400" data-testid="user-list-empty">
          No users found
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3" data-testid="user-list-grid">
          {users.map((user: User) => (
            <UserCard
              key={user.id}
              user={user}
              onEdit={onUserEdit}
              onDelete={onUserDelete}
              showActions={showActions}
              data-testid={`user-card-${user.id}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

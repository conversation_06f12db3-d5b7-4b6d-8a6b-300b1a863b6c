import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import type { BaseFormProps } from '../types';

export interface LabelProps extends BaseFormProps {
  children: React.ReactNode;
  htmlFor?: string;
  color?: 'default' | 'error' | 'warning' | 'success';
}

/**
 * Enhanced Label component for forms
 * Moved from ui/Label with form-specific enhancements
 */
export const Label: React.FC<LabelProps> = ({
  children,
  htmlFor,
  size = 'md',
  required = false,
  disabled = false,
  color = 'default',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const getColor = () => {
    switch (color) {
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      case 'success':
        return colors.success;
      default:
        return disabled ? colors.mutedForeground : colors.text;
    }
  };

  const labelClasses = cn(
    'block font-medium',
    sizeClasses[size],
    disabled && 'opacity-50 cursor-not-allowed',
    required && "after:content-['*'] after:ml-0.5 after:text-red-500",
    className
  );

  return (
    <label
      htmlFor={htmlFor}
      className={labelClasses}
      style={{ color: getColor() }}
      data-testid={testId}
    >
      {children}
    </label>
  );
};

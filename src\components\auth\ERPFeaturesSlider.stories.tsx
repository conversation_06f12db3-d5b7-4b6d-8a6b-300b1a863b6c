import type { Meta, StoryObj } from '@storybook/react-vite';
import { ERPFeaturesSlider } from './ERPFeaturesSlider';

const meta: Meta<typeof ERPFeaturesSlider> = {
  title: 'Auth/ERPFeaturesSlider',
  component: ERPFeaturesSlider,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'An animated slider showcasing ERP features with parallax scrolling effects and smooth transitions.',
      },
    },
  },
  argTypes: {
    autoPlay: {
      control: 'boolean',
      description: 'Enable automatic slide progression',
    },
    autoPlayInterval: {
      control: { type: 'number', min: 1000, max: 10000, step: 500 },
      description: 'Interval between auto slides in milliseconds',
    },
    showDots: {
      control: 'boolean',
      description: 'Show dot indicators at the bottom',
    },
    showArrows: {
      control: 'boolean',
      description: 'Show navigation arrows',
    },
  },
};

export default meta;
type Story = StoryObj<typeof ERPFeaturesSlider>;

export const Default: Story = {
  args: {
    autoPlay: true,
    autoPlayInterval: 5000,
    showDots: true,
    showArrows: true,
  },
  render: args => (
    <div className="h-screen bg-slate-50 dark:bg-slate-900">
      <ERPFeaturesSlider {...args} />
    </div>
  ),
};

export const WithoutAutoPlay: Story = {
  args: {
    autoPlay: false,
    showDots: true,
    showArrows: true,
  },
  render: args => (
    <div className="h-screen bg-slate-50 dark:bg-slate-900">
      <ERPFeaturesSlider {...args} />
    </div>
  ),
};

export const MinimalControls: Story = {
  args: {
    autoPlay: true,
    autoPlayInterval: 3000,
    showDots: false,
    showArrows: false,
  },
  render: args => (
    <div className="h-screen bg-slate-50 dark:bg-slate-900">
      <ERPFeaturesSlider {...args} />
    </div>
  ),
};

export const FastTransition: Story = {
  args: {
    autoPlay: true,
    autoPlayInterval: 2000,
    showDots: true,
    showArrows: true,
  },
  render: args => (
    <div className="h-screen bg-slate-50 dark:bg-slate-900">
      <ERPFeaturesSlider {...args} />
    </div>
  ),
};

const customFeatures = [
  {
    id: 'analytics',
    title: 'Advanced Analytics',
    description: 'Powerful business intelligence and data visualization tools',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
        />
      </svg>
    ),
    benefits: [
      'Real-time dashboards',
      'Predictive analytics',
      'Custom reports',
    ],
  },
  {
    id: 'automation',
    title: 'Process Automation',
    description: 'Streamline workflows and reduce manual tasks',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
        />
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
        />
      </svg>
    ),
    benefits: ['Workflow automation', 'Task scheduling', 'Error reduction'],
  },
];

export const CustomFeatures: Story = {
  args: {
    features: customFeatures,
    autoPlay: true,
    autoPlayInterval: 4000,
    showDots: true,
    showArrows: true,
  },
  render: args => (
    <div className="h-screen bg-slate-50 dark:bg-slate-900">
      <ERPFeaturesSlider {...args} />
    </div>
  ),
};

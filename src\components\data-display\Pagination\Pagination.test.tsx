import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Pagination } from './Pagination';
import { vi } from 'vitest';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#2563eb',
      secondary: '#4f46e5',
      accent: '#7c3aed',
      warning: '#d97706',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      text: '#111827',
      textSecondary: '#6b7280',
      textMuted: '#9ca3af',
      mutedForeground: '#64748b',
      hover: '#f1f5f9',
      shadow: 'rgba(0, 0, 0, 0.1)',
    },
  }),
}));

describe('Pagination', () => {
  const defaultProps = {
    currentRange: '1-20 of 150',
    onNext: vi.fn(),
    onPrev: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders current range text', () => {
    render(<Pagination {...defaultProps} />);
    expect(screen.getByText('1-20 of 150')).toBeInTheDocument();
  });

  it('calls onPrev when previous button is clicked', async () => {
    const user = userEvent.setup();
    render(<Pagination {...defaultProps} />);

    const prevButton = screen.getByRole('button', { name: /previous page/i });
    await user.click(prevButton);

    expect(defaultProps.onPrev).toHaveBeenCalledTimes(1);
  });

  it('calls onNext when next button is clicked', async () => {
    const user = userEvent.setup();
    render(<Pagination {...defaultProps} />);

    const nextButton = screen.getByRole('button', { name: /next page/i });
    await user.click(nextButton);

    expect(defaultProps.onNext).toHaveBeenCalledTimes(1);
  });

  it('applies correct accessibility attributes', () => {
    render(<Pagination {...defaultProps} data-testid="pagination" />);

    expect(screen.getByTestId('pagination')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /previous page/i })).toHaveAttribute('aria-label', 'Previous page');
    expect(screen.getByRole('button', { name: /next page/i })).toHaveAttribute('aria-label', 'Next page');
  });

  it('renders with custom className', () => {
    render(<Pagination {...defaultProps} className="custom-class" data-testid="pagination" />);
    
    const pagination = screen.getByTestId('pagination');
    expect(pagination).toHaveClass('custom-class');
  });
});

// Unified View Mode Selector Types
// Consolidates all view mode switching patterns into a flexible system

export interface ViewModeOption<T = string> {
  id: T;
  name: string;
  icon: React.ReactNode;
  description?: string;
  preview?: string;
  disabled?: boolean;
  shortcut?: string;
}

export type ViewModeVariant = 
  | 'toggle'      // Simple toggle buttons (like ViewToggle)
  | 'switcher'    // Icon-only buttons (like ViewModeSwitcher)
  | 'selector'    // Full cards with descriptions (like discuss ViewModeSelector)
  | 'compact';    // Minimal buttons with icons only

export type ViewModeSize = 'small' | 'medium' | 'large';

export interface ViewModeSelectorProps<T = string> {
  // Core functionality
  options: ViewModeOption<T>[];
  value: T;
  onChange: (value: T) => void;
  
  // Appearance
  variant?: ViewModeVariant;
  size?: ViewModeSize;
  orientation?: 'horizontal' | 'vertical';
  
  // Features
  showLabels?: boolean;
  showDescriptions?: boolean;
  showPreview?: boolean;
  allowDeselect?: boolean;
  
  // Styling
  className?: string;
  buttonClassName?: string;
  
  // Accessibility
  'aria-label'?: string;
  'data-testid'?: string;
}

export interface ViewModePreviewProps {
  option: ViewModeOption;
  isActive: boolean;
  className?: string;
}

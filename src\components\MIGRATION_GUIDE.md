# Component Migration Guide

This guide helps you migrate from the old component structure to the new organized architecture.

## 🚀 Quick Migration Reference

### Form Components
**Old Location**: `src/components/ui/`  
**New Location**: `src/components/forms/`

```tsx
// ❌ Old (deprecated)
import { Button, Input, Label } from '../components/ui';

// ✅ New
import { Button, Input, Label } from '../components/forms';
```

### Global Components
**Old Location**: `src/components/ui/`  
**New Location**: `src/components/global/`

```tsx
// ❌ Old (deprecated)
import { Card, Text, Heading, Separator } from '../components/ui';

// ✅ New
import { Card, Text, Heading, Separator } from '../components/global';
```

### View Mode Components
**Old Components**: `ViewModeSwitcher`, `ViewToggle`  
**New Component**: `ViewModeSelector`

```tsx
// ❌ Old (deprecated)
import { ViewModeSwitcher, ViewToggle } from '../components/ui';

// ✅ New - Unified component
import { ViewModeSelector, simpleViewModes } from '../components/global';

// Migration examples:
// ViewToggle → ViewModeSelector with variant="toggle"
<ViewModeSelector 
  options={simpleViewModes} 
  value={mode} 
  onChange={setMode} 
  variant="toggle" 
  showLabels 
/>

// ViewModeSwitcher → ViewModeSelector with variant="switcher"
<ViewModeSelector 
  options={dataViewModes} 
  value={mode} 
  onChange={setMode} 
  variant="switcher" 
/>
```

### Dropdown Components
**Old Components**: `Dropdown`, `FilterDropdown`  
**New Components**: Enhanced dropdown system

```tsx
// ❌ Old (deprecated)
import { Dropdown, FilterDropdown } from '../components/ui';

// ✅ New - Enhanced system
import { 
  Dropdown, 
  FilterDropdown,
  DropdownBase,
  DropdownTrigger,
  DropdownContent 
} from '../components/global';
```

### Feedback Components
**Old Location**: `src/components/ui/`
**New Location**: `src/components/feedback/`

```tsx
// ❌ Old (deprecated)
import { Modal } from '../components/ui';

// ✅ New
import { Modal } from '../components/feedback';
```

### Data Display Components
**Old Location**: `src/components/ui/`
**New Location**: `src/components/data-display/`

```tsx
// ❌ Old (deprecated)
import { Pagination } from '../components/ui';

// ✅ New
import { Pagination } from '../components/data-display';
```

### Navigation Components
**Old Location**: `src/components/ui/`
**New Location**: `src/components/navigation/`

```tsx
// ❌ Old (deprecated)
import { TopNavigation } from '../components/ui';

// ✅ New
import { TopNavigation } from '../components/navigation';
```

### User Management
**Old Location**: `src/components/`
**New Location**: `src/modules/user-management/`

```tsx
// ❌ Old (deprecated)
import { AddUser, UserList } from '../components';

// ✅ New - Module-based
import { AddUserModal, UserList } from '../modules/user-management';
```

## 📋 Step-by-Step Migration

### 1. Update Import Statements

Use find and replace in your IDE:

**Form Components:**
- Find: `from '../components/ui'` or `from '../../components/ui'`
- Replace with: `from '../components/forms'` (for Button, Input, Label, TextArea)
- Replace with: `from '../components/global'` (for Card, Text, Heading, Separator)

**View Mode Components:**
- Find: `ViewModeSwitcher` or `ViewToggle`
- Replace with: `ViewModeSelector`
- Update props according to the new API

### 2. Update Component Usage

#### Button Migration
```tsx
// ❌ Old
import { Button } from '../components/ui';

// ✅ New
import { Button } from '../components/forms';

// API is mostly the same, but now includes form-specific features
<Button 
  variant="primary" 
  size="md" 
  loading={isSubmitting}
  type="submit"
>
  Save
</Button>
```

#### Input Migration
```tsx
// ❌ Old
import { Input } from '../components/ui';

// ✅ New
import { Input } from '../components/forms';

// Enhanced with better validation and form features
<Input
  label="Email"
  value={email}
  onChange={setEmail}
  error={errors.email}
  required
  clearable
/>
```

#### ViewModeSelector Migration
```tsx
// ❌ Old ViewToggle
import { ViewToggle } from '../components/ui';
<ViewToggle viewMode={mode} onViewModeChange={setMode} />

// ✅ New ViewModeSelector
import { ViewModeSelector, simpleViewModes } from '../components/global';
<ViewModeSelector 
  options={simpleViewModes} 
  value={mode} 
  onChange={setMode} 
  variant="toggle" 
  showLabels 
/>

// ❌ Old ViewModeSwitcher
import { ViewModeSwitcher } from '../components/ui';
<ViewModeSwitcher 
  viewModes={modes} 
  activeViewMode={active} 
  onViewModeChange={onChange} 
/>

// ✅ New ViewModeSelector
import { ViewModeSelector } from '../components/global';
<ViewModeSelector 
  options={modes} 
  value={active} 
  onChange={onChange} 
  variant="switcher" 
/>
```

### 3. Update Type Imports

```tsx
// ❌ Old
import type { ButtonProps, InputProps } from '../components/ui';

// ✅ New
import type { ButtonProps, InputProps } from '../components/forms';
import type { CardProps, TextProps } from '../components/global';
```

## 🔧 Advanced Migration

### Custom Dropdown Migration

If you were using the old Dropdown component with custom logic:

```tsx
// ❌ Old
import { Dropdown } from '../components/ui';

// ✅ New - Composition approach
import { 
  DropdownBase, 
  DropdownTrigger, 
  DropdownContent, 
  DropdownItem 
} from '../components/global';

function CustomDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <DropdownBase isOpen={isOpen} onOpenChange={setIsOpen}>
      <DropdownTrigger>
        <button>Open Menu</button>
      </DropdownTrigger>
      <DropdownContent>
        <DropdownItem onSelect={() => console.log('Action 1')}>
          Action 1
        </DropdownItem>
        <DropdownItem onSelect={() => console.log('Action 2')}>
          Action 2
        </DropdownItem>
      </DropdownContent>
    </DropdownBase>
  );
}
```

### User Management Migration

```tsx
// ❌ Old
import { AddUser, UserList } from '../components';

function UserManagement() {
  return (
    <div>
      <AddUser onUserAdded={refetch} />
      <UserList users={users} />
    </div>
  );
}

// ✅ New
import { AddUserModal, UserList } from '../modules/user-management';

function UserManagement() {
  return (
    <div>
      <AddUserModal onUserAdded={refetch} />
      <UserList 
        onUserEdit={handleEdit}
        onUserDelete={handleDelete}
      />
    </div>
  );
}
```

## 🧪 Testing Migration

After migration, ensure:

1. **Functionality**: All features work as before
2. **Styling**: Visual appearance is consistent
3. **Types**: TypeScript compilation succeeds
4. **Tests**: All tests pass
5. **Storybook**: Stories render correctly

## 🚨 Breaking Changes

### Minimal Breaking Changes
Most migrations are drop-in replacements with the same API.

### API Changes
- **ViewModeSelector**: Unified API replaces separate ViewToggle/ViewModeSwitcher
- **Dropdown**: New composition-based API for advanced use cases
- **User Management**: Now module-based with enhanced features

## 📚 Resources

- [Global Components README](./global/README.md)
- [Form Components Documentation](./forms/README.md)
- [ViewModeSelector Storybook](../stories/ViewModeSelector.stories.tsx)
- [Dropdown System Storybook](../stories/Dropdown.stories.tsx)

## 🆘 Need Help?

If you encounter issues during migration:

1. Check the deprecation warnings in the old components
2. Review the Storybook examples for the new components
3. Look at the test files for usage examples
4. Check the TypeScript types for API differences

## 🎯 Benefits After Migration

- **Better Organization**: Components are logically grouped
- **Enhanced Features**: New components have more capabilities
- **Improved Performance**: Better tree-shaking and optimization
- **Consistent API**: Unified patterns across similar components
- **Better Testing**: More comprehensive test coverage
- **Future-Proof**: Easier to maintain and extend

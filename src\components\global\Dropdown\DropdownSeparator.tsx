import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import type { DropdownSeparatorProps } from './types';

/**
 * Dropdown separator component for visual separation between groups
 */
export const DropdownSeparator: React.FC<DropdownSeparatorProps> = ({
  className = '',
}) => {
  const { colors } = useThemeStore();

  return (
    <div
      className={cn('my-1 border-t', className)}
      style={{ borderColor: colors.border }}
      role="separator"
    />
  );
};

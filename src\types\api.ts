// API Types
// Type definitions for API requests, responses, and related functionality

import type { Pagination, SortConfig, FilterConfig, LoadingState } from './common';

// HTTP Methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

// HTTP Status Codes
export type HttpStatusCode = 
  | 200 | 201 | 202 | 204 // Success
  | 400 | 401 | 403 | 404 | 409 | 422 | 429 // Client Error
  | 500 | 502 | 503 | 504; // Server Error

// Content Types
export type ContentType = 
  | 'application/json'
  | 'application/xml'
  | 'application/x-www-form-urlencoded'
  | 'multipart/form-data'
  | 'text/plain'
  | 'text/html'
  | 'text/csv'
  | 'application/pdf'
  | 'image/jpeg'
  | 'image/png'
  | 'image/gif'
  | 'image/webp';

// API Request Configuration
export interface ApiRequestConfig {
  url: string;
  method: HttpMethod;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTimeout?: number;
  signal?: AbortSignal;
  onUploadProgress?: (progress: ProgressEvent) => void;
  onDownloadProgress?: (progress: ProgressEvent) => void;
}

// API Response
export interface ApiResponse<T = any> {
  data: T;
  status: HttpStatusCode;
  statusText: string;
  headers: Record<string, string>;
  config: ApiRequestConfig;
  meta?: ResponseMeta;
}

// Response Metadata
export interface ResponseMeta {
  timestamp: string;
  requestId: string;
  version: string;
  executionTime: number;
  pagination?: Pagination;
  totalCount?: number;
  cacheHit?: boolean;
  rateLimitRemaining?: number;
  rateLimitReset?: number;
}

// API Error Response
export interface ApiErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
    path: string;
    method: HttpMethod;
    statusCode: HttpStatusCode;
    validation?: ValidationError[];
  };
}

// Validation Error
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

// API Client Configuration
export interface ApiClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers: Record<string, string>;
  interceptors: {
    request: RequestInterceptor[];
    response: ResponseInterceptor[];
  };
  cache: CacheConfig;
  auth: AuthConfig;
  logging: LoggingConfig;
}

// Request Interceptor
export type RequestInterceptor = (config: ApiRequestConfig) => ApiRequestConfig | Promise<ApiRequestConfig>;

// Response Interceptor
export type ResponseInterceptor = (response: ApiResponse) => ApiResponse | Promise<ApiResponse>;

// Cache Configuration
export interface CacheConfig {
  enabled: boolean;
  defaultTimeout: number;
  maxSize: number;
  storage: 'memory' | 'localStorage' | 'sessionStorage';
  keyGenerator: (config: ApiRequestConfig) => string;
}

// Authentication Configuration
export interface AuthConfig {
  type: 'bearer' | 'basic' | 'apikey' | 'oauth2' | 'custom';
  token?: string;
  refreshToken?: string;
  apiKey?: string;
  username?: string;
  password?: string;
  tokenRefreshUrl?: string;
  onTokenRefresh?: (newToken: string) => void;
  onAuthError?: (error: ApiErrorResponse) => void;
}

// Logging Configuration
export interface LoggingConfig {
  enabled: boolean;
  level: 'error' | 'warn' | 'info' | 'debug';
  logRequests: boolean;
  logResponses: boolean;
  logErrors: boolean;
  sensitiveHeaders: string[];
  sensitiveFields: string[];
}

// Query Parameters
export interface QueryParams {
  page?: number;
  limit?: number;
  offset?: number;
  sort?: string | SortConfig[];
  filter?: string | FilterConfig[];
  search?: string;
  fields?: string[];
  include?: string[];
  exclude?: string[];
  expand?: string[];
  [key: string]: any;
}

// List Request
export interface ListRequest {
  pagination?: {
    page: number;
    limit: number;
  };
  sort?: SortConfig[];
  filter?: FilterConfig[];
  search?: string;
  fields?: string[];
  include?: string[];
}

// List Response
export interface ListResponse<T = any> {
  items: T[];
  pagination: Pagination;
  meta: ResponseMeta;
}

// Create Request
export interface CreateRequest<T = any> {
  data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>;
  options?: {
    returnCreated?: boolean;
    validate?: boolean;
  };
}

// Update Request
export interface UpdateRequest<T = any> {
  id: string | number;
  data: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;
  options?: {
    returnUpdated?: boolean;
    validate?: boolean;
    merge?: boolean;
  };
}

// Delete Request
export interface DeleteRequest {
  id: string | number;
  options?: {
    soft?: boolean;
    cascade?: boolean;
  };
}

// Bulk Operation Request
export interface BulkOperationRequest<T = any> {
  operation: 'create' | 'update' | 'delete';
  items: T[];
  options?: {
    stopOnError?: boolean;
    returnResults?: boolean;
    validate?: boolean;
  };
}

// Bulk Operation Response
export interface BulkOperationResponse<T = any> {
  success: T[];
  errors: BulkOperationError[];
  meta: {
    totalProcessed: number;
    successCount: number;
    errorCount: number;
    executionTime: number;
  };
}

// Bulk Operation Error
export interface BulkOperationError {
  index: number;
  item: any;
  error: ApiErrorResponse['error'];
}

// File Upload Request
export interface FileUploadRequest {
  file: File | Blob;
  filename?: string;
  folder?: string;
  metadata?: Record<string, any>;
  options?: {
    overwrite?: boolean;
    generateThumbnail?: boolean;
    compress?: boolean;
  };
}

// File Upload Response
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  thumbnailUrl?: string;
  metadata: Record<string, any>;
  uploadedAt: string;
}

// API Hook State
export interface ApiHookState<T = any> extends LoadingState {
  data: T | null;
  refetch: () => Promise<void>;
  mutate: (data: T) => void;
}

// API Hook Options
export interface ApiHookOptions {
  enabled?: boolean;
  refetchOnMount?: boolean;
  refetchOnWindowFocus?: boolean;
  refetchInterval?: number;
  staleTime?: number;
  cacheTime?: number;
  retry?: boolean | number;
  retryDelay?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiErrorResponse) => void;
}

// Mutation Options
export interface MutationOptions<T = any, V = any> {
  onSuccess?: (data: T, variables: V) => void;
  onError?: (error: ApiErrorResponse, variables: V) => void;
  onSettled?: (data: T | undefined, error: ApiErrorResponse | null, variables: V) => void;
  retry?: boolean | number;
  retryDelay?: number;
}

// WebSocket Message
export interface WebSocketMessage<T = any> {
  type: string;
  data: T;
  id?: string;
  timestamp: string;
  source?: string;
}

// WebSocket Configuration
export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect: boolean;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeat: boolean;
  heartbeatInterval: number;
  onOpen?: (event: Event) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

// Server-Sent Events Configuration
export interface SSEConfig {
  url: string;
  withCredentials?: boolean;
  headers?: Record<string, string>;
  retry?: number;
  onOpen?: (event: Event) => void;
  onMessage?: (event: MessageEvent) => void;
  onError?: (event: Event) => void;
}

// Rate Limiting
export interface RateLimit {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// API Health Check
export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  checks: {
    database: HealthCheckResult;
    cache: HealthCheckResult;
    storage: HealthCheckResult;
    external: HealthCheckResult[];
  };
}

// Health Check Result
export interface HealthCheckResult {
  status: 'pass' | 'fail' | 'warn';
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
}

# CenteredSearchChipInput

An Odoo-style search and filter dropdown panel with a three-column layout featuring Filters, Group By, and Favorites sections. This component provides a comprehensive interface for search and filtering operations similar to Odoo ERP systems.

## Features

- **Odoo-style Design**: Three-column dropdown layout matching Odoo ERP interface
- **Filter Tags**: Active filters displayed as removable tags in the search bar
- **Three Main Sections**:
  - **Filters**: Standard and custom filter options with selection states
  - **Group By**: Data grouping options with dropdown indicators
  - **Favorites**: Saved searches with delete functionality
- **Professional Styling**: Dark theme with accent colors (pink, cyan, yellow)
- **Interactive Elements**: Hover states, selection indicators, and action buttons
- **Theme Integration**: Uses app's theme colors and design system
- **Responsive Design**: Adapts to different screen sizes

## Usage

### Basic Usage

```tsx
import {
  CenteredSearchChipInput,
  FilterTag,
  FilterItem,
  GroupByItem,
  FavoriteItem,
} from '../components/ui';

const MyComponent = () => {
  const [filterTags, setFilterTags] = useState<FilterTag[]>([]);
  const [filterItems, setFilterItems] = useState<FilterItem[]>([
    { id: 'my-quotations', label: 'My Quotations', selected: true },
    { id: 'quotations', label: 'Quotations', selected: false },
    { id: 'sales-orders', label: 'Sales Orders', selected: false },
    { id: 'create-date', label: 'Create Date', hasDropdown: true },
  ]);

  const groupByItems: GroupByItem[] = [
    { id: 'salesperson', label: 'Salesperson' },
    { id: 'customer', label: 'Customer' },
    { id: 'order-date', label: 'Order Date', hasDropdown: true },
  ];

  const favoriteItems: FavoriteItem[] = [
    { id: 'fully-invoiced', label: 'Fully Invoiced', selected: true },
    { id: 'quotations', label: 'Quotations', selected: false },
    {
      id: 'undelivered-complete',
      label: 'Undelivered Complete',
      selected: false,
    },
    { id: 'unpaid-orders', label: 'Unpaid Orders', selected: false },
  ];

  return (
    <CenteredSearchChipInput
      placeholder="Search..."
      filterTags={filterTags}
      filterItems={filterItems}
      groupByItems={groupByItems}
      favoriteItems={favoriteItems}
      onTagRemove={tagId =>
        setFilterTags(prev => prev.filter(tag => tag.id !== tagId))
      }
      onFilterSelect={filterId => console.log('Filter selected:', filterId)}
      onGroupBySelect={groupId => console.log('Group by selected:', groupId)}
      onFavoriteSelect={favoriteId =>
        console.log('Favorite selected:', favoriteId)
      }
      onFavoriteDelete={favoriteId =>
        console.log('Favorite deleted:', favoriteId)
      }
      onAddCustomFilter={() => console.log('Add custom filter')}
      onAddCustomGroup={() => console.log('Add custom group')}
      onSaveCurrentSearch={() => console.log('Save current search')}
      onSearch={query => console.log('Search:', query)}
    />
  );
};
```

### Odoo Sales Example

```tsx
const odooSalesExample = () => {
  const [filterTags, setFilterTags] = useState<FilterTag[]>([
    { id: '1', label: 'Fully Invoiced', removable: true },
    { id: '2', label: 'My Quotations', removable: true },
  ]);

  return (
    <CenteredSearchChipInput
      placeholder="Search..."
      filterTags={filterTags}
      filterItems={[
        { id: 'my-quotations', label: 'My Quotations', selected: true },
        { id: 'quotations', label: 'Quotations' },
        { id: 'sales-orders', label: 'Sales Orders' },
        { id: 'create-date', label: 'Create Date', hasDropdown: true },
      ]}
      groupByItems={[
        { id: 'salesperson', label: 'Salesperson' },
        { id: 'customer', label: 'Customer' },
        { id: 'order-date', label: 'Order Date', hasDropdown: true },
      ]}
      favoriteItems={[
        { id: 'fully-invoiced', label: 'Fully Invoiced', selected: true },
        { id: 'quotations', label: 'Quotations' },
        { id: 'undelivered-complete', label: 'Undelivered Complete' },
        { id: 'unpaid-orders', label: 'Unpaid Orders' },
      ]}
      onTagRemove={tagId =>
        setFilterTags(prev => prev.filter(tag => tag.id !== tagId))
      }
      // ... other handlers
    />
  );
};
```

## Props

### CenteredSearchChipInputProps

| Prop                  | Type                           | Default       | Description                                         |
| --------------------- | ------------------------------ | ------------- | --------------------------------------------------- |
| `placeholder`         | `string`                       | `"Search..."` | Placeholder text for the search input               |
| `filterTags`          | `FilterTag[]`                  | `[]`          | Array of active filter tags displayed in search bar |
| `filterItems`         | `FilterItem[]`                 | `[]`          | Available filter items in the Filters section       |
| `groupByItems`        | `GroupByItem[]`                | `[]`          | Available grouping options in the Group By section  |
| `favoriteItems`       | `FavoriteItem[]`               | `[]`          | Saved favorite searches in the Favorites section    |
| `onSearch`            | `(query: string) => void`      | -             | Callback when search query changes                  |
| `onTagRemove`         | `(tagId: string) => void`      | -             | Callback when a filter tag is removed               |
| `onFilterSelect`      | `(filterId: string) => void`   | -             | Callback when a filter item is selected             |
| `onGroupBySelect`     | `(groupId: string) => void`    | -             | Callback when a group by option is selected         |
| `onFavoriteSelect`    | `(favoriteId: string) => void` | -             | Callback when a favorite is selected                |
| `onFavoriteDelete`    | `(favoriteId: string) => void` | -             | Callback when a favorite is deleted                 |
| `onAddCustomFilter`   | `() => void`                   | -             | Callback when "Add Custom Filter" is clicked        |
| `onAddCustomGroup`    | `() => void`                   | -             | Callback when "Add Custom Group" is clicked         |
| `onSaveCurrentSearch` | `() => void`                   | -             | Callback when "Save current search" is clicked      |
| `className`           | `string`                       | `""`          | Additional CSS classes                              |
| `data-testid`         | `string`                       | -             | Test ID for testing                                 |

### FilterTag

| Property    | Type      | Description                                                 |
| ----------- | --------- | ----------------------------------------------------------- |
| `id`        | `string`  | Unique identifier for the tag                               |
| `label`     | `string`  | Display text for the tag                                    |
| `removable` | `boolean` | Whether the tag can be removed (optional, defaults to true) |

### FilterItem

| Property      | Type         | Description                                            |
| ------------- | ------------ | ------------------------------------------------------ |
| `id`          | `string`     | Unique identifier for the filter                       |
| `label`       | `string`     | Display text for the filter                            |
| `selected`    | `boolean`    | Whether the filter is currently selected (optional)    |
| `hasDropdown` | `boolean`    | Whether the filter has a dropdown indicator (optional) |
| `onClick`     | `() => void` | Custom click handler (optional)                        |

### GroupByItem

| Property      | Type         | Description                                            |
| ------------- | ------------ | ------------------------------------------------------ |
| `id`          | `string`     | Unique identifier for the group option                 |
| `label`       | `string`     | Display text for the group option                      |
| `hasDropdown` | `boolean`    | Whether the option has a dropdown indicator (optional) |
| `onClick`     | `() => void` | Custom click handler (optional)                        |

### FavoriteItem

| Property   | Type         | Description                                           |
| ---------- | ------------ | ----------------------------------------------------- |
| `id`       | `string`     | Unique identifier for the favorite                    |
| `label`    | `string`     | Display text for the favorite                         |
| `selected` | `boolean`    | Whether the favorite is currently selected (optional) |
| `onDelete` | `() => void` | Custom delete handler (optional)                      |
| `onClick`  | `() => void` | Custom click handler (optional)                       |

## Design Specifications

### Color Scheme (Odoo-style)

- **Primary Background**: `#2C2C39` (app theme surface color)
- **Primary Text**: `#FFFFFF` (app theme text color)
- **Secondary Text**: `#B0B0B0` (app theme muted text color)
- **Hover Background**: `#3C3C4F` (app theme hover color)
- **Border/Dividers**: `#41415B` (app theme border color)
- **Tag Background**: `#4E4E68` (app theme tertiary surface color)

### Accent Colors

- **Filters Icon**: `#E91E63` (pink)
- **Group By Icon**: `#00BCD4` (cyan)
- **Favorites Icon**: `#FFC107` (yellow)
- **Trash Hover**: Red (app theme error color)

### Layout

- **Dropdown Width**: 750-800px
- **Border Radius**: 4px
- **Internal Padding**: 16px
- **Section Spacing**: 12px
- **Three-column Layout**: Equal width columns with borders

### Typography

- **Font**: Inter or app's default font
- **Title Size**: 14px bold
- **Item Size**: 13px regular
- **Line Height**: 1.4

### Interactive States

- **Hover**: Background changes to hover color, text remains white
- **Selected**: Highlighted background with checkmark icon
- **Dropdown Indicators**: Chevron down icon for expandable items
- **Delete Actions**: Trash icon with red hover state

## Behavior

- **Dropdown Opening**: Popover opens when clicking the search bar or dropdown arrow
- **Section Navigation**: Three sections displayed side-by-side in the dropdown
- **Filter Selection**: Clicking filter items toggles their selected state
- **Tag Management**: Active filters appear as removable tags in the search bar
- **Responsive**: Collapses to vertical layout on small screens
- **Scrolling**: Scrollbar appears if content overflows within sections

## Accessibility

- ARIA labels for interactive elements
- Keyboard navigation support
- Focus management within dropdown
- Screen reader friendly structure
- High contrast support through theme system

## Testing

The component includes comprehensive tests covering:

- Rendering with different data configurations
- User interactions (search, filter selection, tag removal)
- Dropdown functionality and section navigation
- Callback execution for all interactive elements
- Accessibility features and keyboard navigation

Run tests with:

```bash
npm test CenteredSearchChipInput
```

## Storybook

View the component in Storybook to see the Odoo-style design:

```bash
npm run storybook
```

Navigate to `UI/CenteredSearchChipInput` to see:

- Default state with empty search
- Active tags example
- Odoo Sales example with realistic data
- Interactive functionality demonstrations

## Integration Notes

This component is designed to integrate with ERP-style applications and provides:

- Professional enterprise UI matching Odoo's design language
- Comprehensive filtering and search capabilities
- Scalable data organization through favorites and grouping
- Consistent theming with the application's design system

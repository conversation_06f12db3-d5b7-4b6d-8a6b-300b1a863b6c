import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import {
  RootLayout,
  HomePage,
  LoginPage,
  DashboardPage,
  AppPage,
} from '../pages/pages';

// Define routes using React Router v6
const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'login',
        element: <LoginPage />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'app',
        element: <AppPage />,
      },
    ],
  },
]);

export function AppRouter() {
  return <RouterProvider router={router} />;
}

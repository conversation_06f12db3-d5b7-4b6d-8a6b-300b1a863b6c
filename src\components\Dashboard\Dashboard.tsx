import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import {
  useSearchShortcuts,
  useNavigationShortcuts,
} from '../../hooks/useKeyboardShortcuts';
import { NotificationBar, AppTile, SearchOverlay, TopNavigation, CustomerSupportModal } from '../ui';
import { Separator } from '../global';
import type { SearchResult } from '../ui/SearchOverlay/SearchOverlay';
import { mockApps } from '../../data/mockApps';

export interface DashboardProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    isOnline?: boolean;
  };
  className?: string;
  'data-testid'?: string;
}

const Dashboard: React.FC<DashboardProps> = ({
  user = {
    name: 'Admin User',
    email: '<EMAIL>',
    isOnline: true,
  },
  className = '',
  'data-testid': testId,
}) => {
  const [showNotificationBar, setShowNotificationBar] = useState(true);
  const [showSearchOverlay, setShowSearchOverlay] = useState(false);
  const [showSupportModal, setShowSupportModal] = useState(false);
  const [selectedInactiveApp, setSelectedInactiveApp] = useState<string>('');
  const { isDark, colors } = useThemeStore();
  const navigate = useNavigate();

  // Use mock apps data and separate active/inactive
  const allApps = mockApps;
  const activeApps = allApps.filter(app => app.isActive !== false);
  const inactiveApps = allApps.filter(app => app.isActive === false);

  // Keyboard shortcuts
  useSearchShortcuts(() => setShowSearchOverlay(true));
  useNavigationShortcuts();

  // Handle close overlays event
  useEffect(() => {
    const handleCloseOverlays = () => {
      setShowSearchOverlay(false);
    };

    document.addEventListener('closeOverlays', handleCloseOverlays);
    return () =>
      document.removeEventListener('closeOverlays', handleCloseOverlays);
  }, []);

  const handleAppClick = (appId: string) => {
    const app = allApps.find(a => a.id === appId);
    
    if (!app) {
      console.error(`App with id ${appId} not found`);
      return;
    }

    // If app is inactive, show customer support modal
    if (!app.isActive) {
      console.log(`App ${app.title} is inactive, showing support modal`);
      setSelectedInactiveApp(app.title);
      setShowSupportModal(true);
      return;
    }

    console.log(`Opening app: ${appId}`);
    // Navigate to the app view with the menu parameter
    navigate(`/app?menu=${appId}`);
  };

  // Create search results from active apps only
  const searchResults: SearchResult[] = activeApps.map(app => ({
    id: app.id,
    title: app.title,
    category: app.description || 'Application',
    path: `/app?menu=${app.id}`,
    icon: app.icon,
  }));

  const handleSearchSelect = (result: SearchResult) => {
    console.log(`Navigating to: ${result.path}`);
    // Navigate to the selected app
    navigate(result.path);
  };

  const backgroundStyle = {
    background: isDark
      ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 30%, #334155 70%, #475569 100%)'
      : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 30%, #cbd5e1 70%, #94a3b8 100%)',
    minHeight: '100vh',
    position: 'relative' as const,
  };

  const vignetteStyle = {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: isDark
      ? 'radial-gradient(ellipse at center, transparent 0%, rgba(15, 23, 42, 0.3) 100%)'
      : 'radial-gradient(ellipse at center, transparent 0%, rgba(248, 250, 252, 0.3) 100%)',
    pointerEvents: 'none' as const,
  };

  return (
    <div
      className={`relative ${className}`}
      style={backgroundStyle}
      data-testid={testId || 'dashboard-page'}
    >
      {/* Vignette overlay */}
      <div style={vignetteStyle} />
      {/* Content wrapper with relative positioning */}
      <div className="relative z-10">
        {/* Top Notification Bar */}
        {showNotificationBar && (
          <NotificationBar
            message="This database will expire in 1 month. Register your subscription or buy a subscription."
            type="info"
            onDismiss={() => setShowNotificationBar(false)}
          />
        )}

        {/* Top Navigation */}
        <TopNavigation
          user={user}
          notificationCount={3}
          onNotificationClick={() => console.log('Notifications clicked')}
          onUserClick={() => console.log('User menu clicked')}
        />

        {/* Main Content */}
        <main className="container mx-auto px-6 py-8">
          {/* Header */}
          <div className="mb-8 max-w-5xl mx-auto">
            <h1 className="text-2xl font-bold" style={{ color: colors.text }}>
              Applications
            </h1>
            <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
              Manage your business applications and modules
            </p>
          </div>

          {/* Active Apps Section */}
          <div className="max-w-5xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
              {activeApps.map((app, index) => (
                <div
                  key={app.id}
                  className="animate-fade-in-up"
                  style={{
                    animationDelay: `${index * 50}ms`,
                    animationFillMode: 'both',
                  }}
                >
                  <AppTile
                    title={app.title}
                    description={app.description}
                    icon={app.icon}
                    color={app.color}
                    gradient={app.gradient}
                    isActive={app.isActive}
                    isPremium={app.isPremium}
                    onClick={() => handleAppClick(app.id)}
                    data-testid={`app-tile-${app.id}`}
                  />
                </div>
              ))}
            </div>

            {/* Separator and Inactive Apps */}
            {inactiveApps.length > 0 && (
              <>
                <div className="my-12">
                  <Separator className="mb-6" />
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                      <h2 className="text-lg font-semibold" style={{ color: colors.text }}>
                        Inactive Applications
                      </h2>
                    </div>
                    <span
                      className="text-sm px-2 py-1 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400"
                    >
                      Contact support to activate
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
                  {inactiveApps.map((app, index) => (
                    <div
                      key={app.id}
                      className="animate-fade-in-up"
                      style={{
                        animationDelay: `${(activeApps.length + index) * 50}ms`,
                        animationFillMode: 'both',
                      }}
                    >
                      <AppTile
                        title={app.title}
                        description={app.description}
                        icon={app.icon}
                        color={app.color}
                        gradient={app.gradient}
                        isActive={app.isActive}
                        isPremium={app.isPremium}
                        onClick={() => handleAppClick(app.id)}
                        data-testid={`app-tile-${app.id}`}
                      />
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </main>
      </div>

      {/* Search Overlay */}
      <SearchOverlay
        isOpen={showSearchOverlay}
        onClose={() => setShowSearchOverlay(false)}
        onSelect={handleSearchSelect}
        results={searchResults}
        placeholder="Search active applications..."
        data-testid="search-overlay"
      />

      {/* Customer Support Modal */}
      <CustomerSupportModal
        isOpen={showSupportModal}
        onClose={() => {
          setShowSupportModal(false);
          setSelectedInactiveApp('');
        }}
        appName={selectedInactiveApp}
      />
    </div>
  );
};

export default Dashboard;

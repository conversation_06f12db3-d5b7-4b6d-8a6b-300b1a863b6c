// Role Service - API operations for role management
import type { UserRole } from '../types';

export interface RoleServiceConfig {
  baseUrl?: string;
  timeout?: number;
}

export interface Role {
  id: string;
  name: UserRole;
  displayName: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

class RoleService {
  private baseUrl: string;
  private timeout: number;

  constructor(config: RoleServiceConfig = {}) {
    this.baseUrl = config.baseUrl || '/api';
    this.timeout = config.timeout || 5000;
  }

  /**
   * Fetch all roles
   */
  async getRoles(): Promise<Role[]> {
    const response = await fetch(`${this.baseUrl}/roles`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch roles: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Get a single role by ID
   */
  async getRoleById(id: string): Promise<Role> {
    const response = await fetch(`${this.baseUrl}/roles/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch role: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Create a new role
   */
  async createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> {
    const response = await fetch(`${this.baseUrl}/roles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(roleData),
    });

    if (!response.ok) {
      throw new Error(`Failed to create role: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Update an existing role
   */
  async updateRole(id: string, roleData: Partial<Omit<Role, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Role> {
    const response = await fetch(`${this.baseUrl}/roles/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(roleData),
    });

    if (!response.ok) {
      throw new Error(`Failed to update role: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Delete a role
   */
  async deleteRole(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/roles/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete role: ${response.statusText}`);
    }
  }
}

// Export singleton instance
export const roleService = new RoleService();

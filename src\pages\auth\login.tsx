import { useNavigate } from 'react-router-dom';
import { LoginScreen } from '../../components/auth/LoginScreen';
import { ErrorBoundary } from '../../components/common';

/**
 * Login Page Component
 * 
 * Handles user authentication and login functionality.
 * Redirects to dashboard upon successful authentication.
 */
export default function LoginPage() {
  const navigate = useNavigate();

  const handleLogin = (credentials: any) => {
    console.log('Login successful:', credentials);
    // TODO: Implement actual login logic
    // Navigate to dashboard after successful login
    navigate('/dashboard');
  };

  const handleAccessRequest = (request: any) => {
    console.log('Access request submitted:', request);
    // TODO: Implement access request logic
  };

  return (
    <ErrorBoundary
      level="page"
      componentName="LoginPage"
      enableAutoRecovery={true}
      enableReporting={true}
    >
      <LoginScreen
        onLogin={handleLogin}
        onAccessRequest={handleAccessRequest}
        data-testid="login-page"
      />
    </ErrorBoundary>
  );
}

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface ERPFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  benefits: string[];
  image?: string;
}

export interface ERPFeaturesSliderProps {
  features?: ERPFeature[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  className?: string;
  'data-testid'?: string;
}

const defaultFeatures: ERPFeature[] = [
  {
    id: 'inventory',
    title: 'Smart Inventory Management',
    description:
      'Real-time tracking and automated reordering for optimal stock levels',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
        />
      </svg>
    ),
    benefits: [
      'Reduce stockouts by 85%',
      'Optimize warehouse space',
      'Automated purchase orders',
    ],
  },
  {
    id: 'finance',
    title: 'Financial Analytics',
    description:
      'Comprehensive financial reporting and real-time business insights',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
        />
      </svg>
    ),
    benefits: [
      'Real-time P&L tracking',
      'Automated invoicing',
      'Tax compliance',
    ],
  },
  {
    id: 'crm',
    title: 'Customer Relationship Management',
    description: 'Streamline customer interactions and boost sales performance',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
        />
      </svg>
    ),
    benefits: [
      '360° customer view',
      'Sales pipeline tracking',
      'Automated follow-ups',
    ],
  },
  {
    id: 'hr',
    title: 'Human Resources',
    description:
      'Complete HR management from recruitment to performance tracking',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
        />
      </svg>
    ),
    benefits: [
      'Streamlined payroll',
      'Performance analytics',
      'Employee self-service',
    ],
  },
  {
    id: 'supply-chain',
    title: 'Supply Chain Optimization',
    description: 'End-to-end supply chain visibility and optimization',
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
        />
      </svg>
    ),
    benefits: [
      'Vendor management',
      'Procurement automation',
      'Cost optimization',
    ],
  },
];

export function ERPFeaturesSlider({
  features = defaultFeatures,
  autoPlay = true,
  autoPlayInterval = 5000,
  showDots = true,
  showArrows = true,
  className = '',
  'data-testid': testId,
}: ERPFeaturesSliderProps) {
  const { colors } = useThemeStore();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const nextSlide = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentSlide(prev => (prev + 1) % features.length);
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, features.length]);

  // Auto-play functionality
  useEffect(() => {
    if (autoPlay && !isPaused && features.length > 1) {
      intervalRef.current = setInterval(() => {
        nextSlide();
      }, autoPlayInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [
    autoPlay,
    isPaused,
    currentSlide,
    autoPlayInterval,
    features.length,
    nextSlide,
  ]);

  const prevSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentSlide(prev => (prev - 1 + features.length) % features.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const goToSlide = (index: number) => {
    if (isAnimating || index === currentSlide) return;
    setIsAnimating(true);
    setCurrentSlide(index);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const handleMouseEnter = () => setIsPaused(true);
  const handleMouseLeave = () => setIsPaused(false);

  const currentFeature = features[currentSlide];

  return (
    <div
      ref={containerRef}
      className={`relative h-full overflow-hidden ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-testid={testId}
    >
      {/* Background with parallax effect */}
      <div
        className="absolute inset-0 transition-transform duration-1000 ease-out"
        style={{
          background: `linear-gradient(135deg, ${colors.primary}15, ${colors.secondary}10)`,
          transform: `translateX(${currentSlide * -2}px)`,
        }}
      />

      {/* Main content */}
      <div className="relative h-full flex flex-col justify-center p-8 lg:p-12">
        {/* Feature content */}
        <div
          className={`transition-all duration-500 ease-out ${
            isAnimating
              ? 'opacity-0 transform translate-y-4'
              : 'opacity-100 transform translate-y-0'
          }`}
        >
          {/* Icon */}
          <div
            className="w-16 h-16 rounded-2xl flex items-center justify-center mb-6"
            style={{
              backgroundColor: `${colors.primary}20`,
              color: colors.primary,
            }}
          >
            {currentFeature.icon}
          </div>

          {/* Title */}
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            {currentFeature.title}
          </h2>

          {/* Description */}
          <p className="text-lg text-slate-600 dark:text-slate-400 mb-8 leading-relaxed">
            {currentFeature.description}
          </p>

          {/* Benefits */}
          <div className="space-y-3 mb-8">
            {currentFeature.benefits.map((benefit, index) => (
              <div
                key={index}
                className="flex items-center gap-3"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <div
                  className="w-2 h-2 rounded-full flex-shrink-0"
                  style={{ backgroundColor: colors.primary }}
                />
                <span className="text-slate-700 dark:text-slate-300">
                  {benefit}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation arrows */}
        {showArrows && features.length > 1 && (
          <>
            <button
              onClick={prevSlide}
              disabled={isAnimating}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-slate-700 dark:text-slate-300 hover:bg-white/20 transition-all duration-200 disabled:opacity-50"
              aria-label="Previous feature"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <button
              onClick={nextSlide}
              disabled={isAnimating}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-slate-700 dark:text-slate-300 hover:bg-white/20 transition-all duration-200 disabled:opacity-50"
              aria-label="Next feature"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </>
        )}

        {/* Dots indicator */}
        {showDots && features.length > 1 && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
            {features.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                disabled={isAnimating}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentSlide ? 'scale-125' : 'hover:scale-110'
                }`}
                style={{
                  backgroundColor:
                    index === currentSlide
                      ? colors.primary
                      : `${colors.primary}40`,
                }}
                aria-label={`Go to feature ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

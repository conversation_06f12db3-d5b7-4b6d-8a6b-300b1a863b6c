import { useState, useCallback } from 'react';
import type { UserFormData, UserFormErrors } from '../types';
import type { UseUserFormReturn } from './types';

const initialFormData: UserFormData = {
  name: '',
  email: '',
  role: 'user',
};

const initialErrors: UserFormErrors = {};

/**
 * Hook for managing user form state and validation
 */
export function useUserForm(
  onSubmit?: (data: UserFormData) => Promise<void>
): UseUserFormReturn {
  const [formData, setFormData] = useState<UserFormData>(initialFormData);
  const [errors, setErrors] = useState<UserFormErrors>(initialErrors);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateField = useCallback((field: keyof UserFormData, value: string): string | undefined => {
    switch (field) {
      case 'name':
        if (!value.trim()) return 'Name is required';
        if (value.trim().length < 2) return 'Name must be at least 2 characters';
        return undefined;
      
      case 'email':
        if (!value.trim()) return 'Email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return 'Please enter a valid email address';
        return undefined;
      
      case 'role':
        if (!value) return 'Role is required';
        return undefined;
      
      default:
        return undefined;
    }
  }, []);

  const handleChange = useCallback((field: keyof UserFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const validateForm = useCallback((): boolean => {
    const newErrors: UserFormErrors = {};
    
    Object.keys(formData).forEach(key => {
      const field = key as keyof UserFormData;
      const error = validateField(field, formData[field]);
      if (error) {
        newErrors[field] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, validateField]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!onSubmit) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      setFormData(initialFormData);
      setErrors(initialErrors);
    } catch (error) {
      setErrors({ general: error instanceof Error ? error.message : 'An error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, onSubmit]);

  const reset = useCallback(() => {
    setFormData(initialFormData);
    setErrors(initialErrors);
    setIsSubmitting(false);
  }, []);

  const isValid = Object.values(formData).every(value => value.trim() !== '') && 
                  Object.keys(errors).length === 0;

  return {
    formData,
    errors,
    isSubmitting,
    isValid,
    handleChange,
    handleSubmit,
    reset,
    setErrors,
  };
}

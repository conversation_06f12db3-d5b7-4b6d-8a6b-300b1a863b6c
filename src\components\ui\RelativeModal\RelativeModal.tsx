import React, { useEffect, useRef, useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface RelativeModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  triggerRef: React.RefObject<HTMLElement | null>;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  className?: string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  offset?: number;
}

export const RelativeModal: React.FC<RelativeModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  triggerRef,
  size = 'md',
  showCloseButton = true,
  closeOnBackdropClick = true,
  className,
  position = 'auto',
  offset = 8,
}) => {
  const { colors, isDark } = useThemeStore();
  const modalRef = useRef<HTMLDivElement>(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, left: 0 });
  const [actualPosition, setActualPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('bottom');

  // Calculate modal position relative to trigger
  useEffect(() => {
    if (!isOpen || !triggerRef.current || !modalRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const modalRect = modalRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;
    let finalPosition = position;

    // Auto-position logic
    if (position === 'auto') {
      const spaceBelow = viewportHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;
      const spaceRight = viewportWidth - triggerRect.right;
      const spaceLeft = triggerRect.left;

      // Prefer bottom, then top, then right, then left
      if (spaceBelow >= modalRect.height + offset) {
        finalPosition = 'bottom';
      } else if (spaceAbove >= modalRect.height + offset) {
        finalPosition = 'top';
      } else if (spaceRight >= modalRect.width + offset) {
        finalPosition = 'right';
      } else {
        finalPosition = 'left';
      }
    }

    // Calculate position based on final position
    switch (finalPosition) {
      case 'bottom':
        top = triggerRect.bottom + offset;
        left = triggerRect.left + (triggerRect.width / 2) - (modalRect.width / 2);
        break;
      case 'top':
        top = triggerRect.top - modalRect.height - offset;
        left = triggerRect.left + (triggerRect.width / 2) - (modalRect.width / 2);
        break;
      case 'right':
        top = triggerRect.top + (triggerRect.height / 2) - (modalRect.height / 2);
        left = triggerRect.right + offset;
        break;
      case 'left':
        top = triggerRect.top + (triggerRect.height / 2) - (modalRect.height / 2);
        left = triggerRect.left - modalRect.width - offset;
        break;
    }

    // Ensure modal stays within viewport bounds
    left = Math.max(8, Math.min(left, viewportWidth - modalRect.width - 8));
    top = Math.max(8, Math.min(top, viewportHeight - modalRect.height - 8));

    setModalPosition({ top, left });
    setActualPosition(finalPosition);
  }, [isOpen, triggerRef, position, offset]);

  // Handle body scroll lock when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closeOnBackdropClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const sizeClasses = {
    sm: 'w-80',
    md: 'w-96',
    lg: 'w-[32rem]',
    xl: 'w-[48rem]',
  };

  return (
    <div
      className="fixed inset-0 z-[9999]"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div
        className={cn(
          'absolute inset-0 transition-all duration-300 ease-out',
          isOpen ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          background: 'rgba(0, 0, 0, 0.4)',
          backdropFilter: 'blur(4px)',
          WebkitBackdropFilter: 'blur(4px)',
        }}
      />

      {/* Modal Content */}
      <div
        ref={modalRef}
        className={cn(
          'absolute max-h-[80vh] overflow-hidden shadow-2xl',
          'rounded-2xl transform transition-all duration-300 ease-out',
          sizeClasses[size],
          isOpen ? 'scale-100 opacity-100' : 'scale-90 opacity-0',
          className
        )}
        style={{
          top: modalPosition.top,
          left: modalPosition.left,
          background: isDark
            ? 'rgba(15, 23, 42, 0.95)'
            : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          border: isDark 
            ? '1px solid rgba(71, 85, 105, 0.3)'
            : '1px solid rgba(203, 213, 225, 0.4)',
          boxShadow: isDark
            ? '0 20px 60px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            : '0 20px 60px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
        }}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div
            className="flex items-center justify-between px-6 py-4 border-b"
            style={{ borderColor: colors.border }}
          >
            {title && (
              <h2
                className="text-xl font-semibold"
                style={{ color: colors.text }}
              >
                {title}
              </h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className={cn(
                  'p-2 rounded-lg transition-colors duration-200',
                  'hover:bg-gray-100 dark:hover:bg-gray-800',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                )}
                aria-label="Close modal"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  style={{ color: colors.textSecondary }}
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(80vh-80px)]">
          {children}
        </div>

        {/* Position indicator arrow */}
        <div
          className={cn(
            'absolute w-3 h-3 transform rotate-45',
            actualPosition === 'bottom' && '-top-1.5 left-1/2 -translate-x-1/2',
            actualPosition === 'top' && '-bottom-1.5 left-1/2 -translate-x-1/2',
            actualPosition === 'right' && '-left-1.5 top-1/2 -translate-y-1/2',
            actualPosition === 'left' && '-right-1.5 top-1/2 -translate-y-1/2'
          )}
          style={{
            background: isDark
              ? 'rgba(15, 23, 42, 0.95)'
              : 'rgba(255, 255, 255, 0.95)',
            border: isDark 
              ? '1px solid rgba(71, 85, 105, 0.3)'
              : '1px solid rgba(203, 213, 225, 0.4)',
          }}
        />
      </div>
    </div>
  );
};

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import UserManagementModule from '../UserManagementModule';
import { useThemeStore } from '../../../stores/themeStore';

// Mock dependencies
vi.mock('../../../stores/themeStore');
vi.mock('../../../data/mockApps', () => ({
  getAppById: vi.fn(() => ({
    id: '2',
    name: 'User Management',
    icon: '👥',
    description: 'Manage users and permissions',
  })),
}));

vi.mock('../components/UserList', () => ({
  UserList: () => <div data-testid="user-list">User List Component</div>,
}));

vi.mock('../components/AddUserModal', () => ({
  AddUserModal: () => <div data-testid="add-user-modal">Add User Modal</div>,
}));

vi.mock('../../components/layout/DynamicAppHeader', () => ({
  default: ({ app, user, view }: any) => (
    <div data-testid="app-header">
      <div>{app.name}</div>
      <div>{view.title}</div>
    </div>
  ),
}));

vi.mock('../../components/layout/AppDynamicContent', () => ({
  default: ({ children, view }: any) => (
    <div data-testid="app-content">
      <div>{view.title}</div>
      {children}
    </div>
  ),
}));

const mockUseThemeStore = vi.mocked(useThemeStore);

const renderWithRouter = (component: React.ReactElement, initialEntries = ['/']) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('UserManagementModule', () => {
  beforeEach(() => {
    mockUseThemeStore.mockReturnValue({
      colors: {
        background: '#ffffff',
        text: '#000000',
        textSecondary: '#666666',
        primary: '#3b82f6',
      },
    });
  });

  it('renders user management module with default view', () => {
    renderWithRouter(<UserManagementModule />);

    expect(screen.getByTestId('app-header')).toBeInTheDocument();
    expect(screen.getByTestId('app-content')).toBeInTheDocument();
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByTestId('user-list')).toBeInTheDocument();
    expect(screen.getByTestId('add-user-modal')).toBeInTheDocument();
  });

  it('renders with custom className and testId', () => {
    renderWithRouter(
      <UserManagementModule 
        className="custom-class" 
        data-testid="custom-test-id" 
      />
    );

    const module = screen.getByTestId('custom-test-id');
    expect(module).toBeInTheDocument();
    expect(module).toHaveClass('custom-class');
  });

  it('applies theme colors correctly', () => {
    const customColors = {
      background: '#f8f9fa',
      text: '#212529',
      textSecondary: '#6c757d',
      primary: '#007bff',
    };

    mockUseThemeStore.mockReturnValue({
      colors: customColors,
    });

    renderWithRouter(<UserManagementModule data-testid="themed-module" />);

    const module = screen.getByTestId('themed-module');
    expect(module).toHaveStyle({ backgroundColor: customColors.background });
  });
});

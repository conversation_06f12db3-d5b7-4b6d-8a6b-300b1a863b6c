import type {
  AppConfiguration,
  FeatureFlags,
} from '../types/configuration';
import { getAppConfig, APP_CONFIG, validateConfig } from '../config/app.config';

// Re-export types for backward compatibility
export type { AppConfiguration as AppConfig } from '../types/configuration';

/**
 * Loads the centralized application configuration
 * @returns Configuration object
 */
export function loadConfig(): AppConfiguration {
  try {
    const config = getAppConfig();
    console.log('Configuration loaded successfully from centralized config');
    return config;
  } catch (error) {
    console.error('Error loading configuration:', error);
    // Return the default config to prevent app crashes
    return APP_CONFIG;
  }
}

/**
 * Converts centralized config to store-compatible format
 * @param config Centralized application configuration
 * @returns Store-compatible configuration
 */
export function convertToStoreConfig(config: AppConfiguration): {
  featureFlags: FeatureFlags;
  configuration: AppConfiguration;
} {
  // Extract feature flags from the centralized config
  const featureFlags: FeatureFlags = {
    enableAnalytics: config.features.enableAnalytics,
    enableNotifications: config.features.enableNotifications,
    enableDarkMode: config.features.enableDarkMode,
    enableBetaFeatures: config.features.enableBetaFeatures,
    enableAdvancedSearch: config.features.enableAdvancedSearch,
    enableRealTimeUpdates: config.features.enableRealTimeUpdates,
    enableExperimentalUI: config.features.enableExperimentalUI,
    enableDebugMode: config.features.enableDebugMode,
  };

  // Return the configuration as-is since it's already in the correct format
  return {
    featureFlags,
    configuration: config,
  };
}

/**
 * Loads configuration and initializes the app store
 * @returns Transformed configuration for the app store
 */
export function initializeAppConfig() {
  try {
    const config = loadConfig();
    const storeConfig = convertToStoreConfig(config);

    console.log('App configuration initialized:', storeConfig);
    return storeConfig;
  } catch (error) {
    console.error('Failed to initialize app configuration:', error);
    // Return defaults
    const defaultConfig = convertToStoreConfig(APP_CONFIG);
    return defaultConfig;
  }
}

// Re-export validation function from centralized config
export { validateConfig } from '../config/app.config';

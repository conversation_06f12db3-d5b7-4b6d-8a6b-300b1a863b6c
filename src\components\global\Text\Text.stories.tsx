import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Text } from './Text';

const meta: Meta<typeof Text> = {
  title: 'Global/Text',
  component: Text,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible text component with various styling options for consistent typography across the application.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['body', 'caption', 'overline', 'subtitle1', 'subtitle2', 'label', 'helper'],
      description: 'Text variant with predefined styling',
    },
    size: {
      control: { type: 'select' },
      options: ['xs', 'sm', 'base', 'lg', 'xl', '2xl'],
      description: 'Text size',
    },
    weight: {
      control: { type: 'select' },
      options: ['light', 'normal', 'medium', 'semibold', 'bold'],
      description: 'Font weight',
    },
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'muted', 'error', 'warning', 'success', 'info', 'inherit', 'inverse'],
      description: 'Text color',
    },
    align: {
      control: { type: 'select' },
      options: ['left', 'center', 'right', 'justify'],
      description: 'Text alignment',
    },
    transform: {
      control: { type: 'select' },
      options: ['none', 'uppercase', 'lowercase', 'capitalize'],
      description: 'Text transformation',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default text
export const Default: Story = {
  args: {
    children: 'This is default body text with normal styling.',
  },
};

// Text variants
export const Variants: Story = {
  render: () => (
    <div className="space-y-2">
      <Text variant="body">Body text - regular paragraph content</Text>
      <Text variant="caption">Caption text - smaller descriptive text</Text>
      <Text variant="overline">OVERLINE TEXT - UPPERCASE LABELS</Text>
      <Text variant="subtitle1">Subtitle 1 - larger secondary headings</Text>
      <Text variant="subtitle2">Subtitle 2 - medium secondary headings</Text>
      <Text variant="label">Label text - form labels and UI text</Text>
      <Text variant="helper">Helper text - additional information and guidance</Text>
    </div>
  ),
};

// Text sizes
export const Sizes: Story = {
  render: () => (
    <div className="space-y-2">
      <Text size="xs">Extra small text</Text>
      <Text size="sm">Small text</Text>
      <Text size="base">Base text (default)</Text>
      <Text size="lg">Large text</Text>
      <Text size="xl">Extra large text</Text>
      <Text size="2xl">2X large text</Text>
    </div>
  ),
};

// Font weights
export const Weights: Story = {
  render: () => (
    <div className="space-y-2">
      <Text weight="light">Light weight text</Text>
      <Text weight="normal">Normal weight text</Text>
      <Text weight="medium">Medium weight text</Text>
      <Text weight="semibold">Semibold weight text</Text>
      <Text weight="bold">Bold weight text</Text>
    </div>
  ),
};

// Text colors
export const Colors: Story = {
  render: () => (
    <div className="space-y-2">
      <Text color="primary">Primary color text</Text>
      <Text color="secondary">Secondary color text</Text>
      <Text color="muted">Muted color text</Text>
      <Text color="error">Error color text</Text>
      <Text color="warning">Warning color text</Text>
      <Text color="success">Success color text</Text>
      <Text color="info">Info color text</Text>
    </div>
  ),
};

// Text alignment
export const Alignment: Story = {
  render: () => (
    <div className="space-y-4 w-64">
      <Text align="left">Left aligned text content</Text>
      <Text align="center">Center aligned text content</Text>
      <Text align="right">Right aligned text content</Text>
      <Text align="justify">Justified text content that will wrap to multiple lines and be justified across the full width</Text>
    </div>
  ),
};

// Text transformation
export const Transformation: Story = {
  render: () => (
    <div className="space-y-2">
      <Text transform="none">Normal text transformation</Text>
      <Text transform="uppercase">Uppercase text transformation</Text>
      <Text transform="lowercase">LOWERCASE TEXT TRANSFORMATION</Text>
      <Text transform="capitalize">capitalize text transformation</Text>
    </div>
  ),
};

// Truncated text
export const Truncated: Story = {
  render: () => (
    <div className="space-y-4 w-48">
      <Text truncate>
        This is a very long text that will be truncated when it exceeds the container width
      </Text>
      <Text lineClamp={2}>
        This is a longer text that will be clamped to exactly two lines and show an ellipsis when it exceeds that limit. This should demonstrate the line clamping functionality.
      </Text>
      <Text lineClamp={3}>
        This is an even longer text that will be clamped to exactly three lines. It should show multiple lines of content before being cut off with an ellipsis. This demonstrates how the line clamp feature works with longer content that spans multiple lines.
      </Text>
    </div>
  ),
};

// Different HTML elements
export const Elements: Story = {
  render: () => (
    <div className="space-y-2">
      <Text as="p">Paragraph element</Text>
      <Text as="span">Span element</Text>
      <Text as="div">Div element</Text>
      <Text as="label">Label element</Text>
      <Text as="small">Small element</Text>
      <Text as="strong">Strong element</Text>
      <Text as="em">Emphasis element</Text>
    </div>
  ),
};

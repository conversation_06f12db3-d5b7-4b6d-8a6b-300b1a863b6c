// MSW utilities and helpers
import { HttpResponse } from 'msw';

// Simulate network delay
export const delay = (ms: number = 500): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Random delay between min and max
export const randomDelay = (
  min: number = 200,
  max: number = 1000
): Promise<void> => {
  const ms = Math.floor(Math.random() * (max - min + 1)) + min;
  return delay(ms);
};

// Error simulation utilities
export interface ErrorScenario {
  probability: number; // 0-1
  status: number;
  message: string;
}

export const simulateError = (
  scenarios: ErrorScenario[] = []
): HttpResponse | null => {
  for (const scenario of scenarios) {
    if (Math.random() < scenario.probability) {
      return HttpResponse.json(
        { error: scenario.message },
        { status: scenario.status }
      );
    }
  }
  return null;
};

// Common error scenarios
export const commonErrors = {
  serverError: {
    probability: 0.05,
    status: 500,
    message: 'Internal server error',
  },
  notFound: { probability: 0.02, status: 404, message: 'Resource not found' },
  unauthorized: { probability: 0.01, status: 401, message: 'Unauthorized' },
  forbidden: { probability: 0.01, status: 403, message: 'Forbidden' },
  badRequest: { probability: 0.03, status: 400, message: 'Bad request' },
  timeout: { probability: 0.02, status: 408, message: 'Request timeout' },
};

// Pagination utilities
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export const paginate = <T>(
  data: T[],
  params: PaginationParams = {}
): PaginatedResponse<T> => {
  const page = Math.max(1, params.page || 1);
  const limit = Math.max(1, Math.min(100, params.limit || 25));
  const offset = params.offset || (page - 1) * limit;

  const total = data.length;
  const totalPages = Math.ceil(total / limit);
  const paginatedData = data.slice(offset, offset + limit);

  return {
    data: paginatedData,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
};

// Search utilities
export const fuzzySearch = <T>(
  data: T[],
  query: string,
  searchFields: (keyof T)[]
): T[] => {
  if (!query.trim()) return data;

  const lowercaseQuery = query.toLowerCase();

  return data.filter(item =>
    searchFields.some(field => {
      const value = item[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(lowercaseQuery);
      }
      return false;
    })
  );
};

// Sorting utilities
export type SortDirection = 'asc' | 'desc';

export const sortData = <T>(
  data: T[],
  field: keyof T,
  direction: SortDirection = 'asc'
): T[] => {
  return [...data].sort((a, b) => {
    const aValue = a[field];
    const bValue = b[field];

    if (aValue === bValue) return 0;

    const comparison = aValue < bValue ? -1 : 1;
    return direction === 'asc' ? comparison : -comparison;
  });
};

// Filter utilities
export type FilterOperator =
  | 'eq'
  | 'ne'
  | 'gt'
  | 'gte'
  | 'lt'
  | 'lte'
  | 'in'
  | 'nin'
  | 'contains';

export interface FilterCondition<T> {
  field: keyof T;
  operator: FilterOperator;
  value: any;
}

export const filterData = <T>(
  data: T[],
  conditions: FilterCondition<T>[]
): T[] => {
  return data.filter(item =>
    conditions.every(condition => {
      const itemValue = item[condition.field];
      const { operator, value } = condition;

      switch (operator) {
        case 'eq':
          return itemValue === value;
        case 'ne':
          return itemValue !== value;
        case 'gt':
          return itemValue > value;
        case 'gte':
          return itemValue >= value;
        case 'lt':
          return itemValue < value;
        case 'lte':
          return itemValue <= value;
        case 'in':
          return Array.isArray(value) && value.includes(itemValue);
        case 'nin':
          return Array.isArray(value) && !value.includes(itemValue);
        case 'contains':
          return (
            typeof itemValue === 'string' &&
            typeof value === 'string' &&
            itemValue.toLowerCase().includes(value.toLowerCase())
          );
        default:
          return true;
      }
    })
  );
};

// Response helpers
export const successResponse = <T>(data: T, status: number = 200) => {
  return HttpResponse.json(data, { status });
};

export const errorResponse = (message: string, status: number = 400) => {
  return HttpResponse.json({ error: message }, { status });
};

export const notFoundResponse = (resource: string = 'Resource') => {
  return HttpResponse.json({ error: `${resource} not found` }, { status: 404 });
};

export const unauthorizedResponse = (message: string = 'Unauthorized') => {
  return HttpResponse.json({ error: message }, { status: 401 });
};

export const forbiddenResponse = (message: string = 'Forbidden') => {
  return HttpResponse.json({ error: message }, { status: 403 });
};

// Request parsing utilities
export const parseQueryParams = (url: URL) => {
  const params: Record<string, any> = {};

  url.searchParams.forEach((value, key) => {
    // Handle arrays (e.g., ?tags=tag1&tags=tag2)
    if (params[key]) {
      if (Array.isArray(params[key])) {
        params[key].push(value);
      } else {
        params[key] = [params[key], value];
      }
    } else {
      // Try to parse as number or boolean
      if (value === 'true') {
        params[key] = true;
      } else if (value === 'false') {
        params[key] = false;
      } else if (!isNaN(Number(value)) && value !== '') {
        params[key] = Number(value);
      } else {
        params[key] = value;
      }
    }
  });

  return params;
};

// Authentication utilities
export const extractBearerToken = (
  authHeader: string | null
): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.replace('Bearer ', '');
};

export const validateToken = (token: string | null): string | null => {
  if (!token || !token.startsWith('mock-jwt-token-')) {
    return null;
  }
  return token.replace('mock-jwt-token-', '');
};

// Data generation utilities
export const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const generateTimestamp = (): string => {
  return new Date().toISOString();
};

// Environment utilities
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV;
};

export const getEnvironment = (): string => {
  return import.meta.env.MODE || 'development';
};

// Form Component Types
// Shared types for all form components

export type FormSize = 'sm' | 'md' | 'lg';
export type FormVariant = 'default' | 'filled' | 'outlined';
export type FormValidationState = 'default' | 'success' | 'warning' | 'error';

export interface BaseFormProps {
  size?: FormSize;
  variant?: FormVariant;
  disabled?: boolean;
  required?: boolean;
  fullWidth?: boolean;
  className?: string;
  'data-testid'?: string;
}

export interface FormFieldProps extends BaseFormProps {
  label?: string;
  error?: string;
  helperText?: string;
  validationState?: FormValidationState;
}

export interface FormControlProps extends FormFieldProps {
  id?: string;
  name?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export interface FormValidationRule {
  required?: boolean | string;
  minLength?: number | { value: number; message: string };
  maxLength?: number | { value: number; message: string };
  pattern?: RegExp | { value: RegExp; message: string };
  validate?: (value: any) => boolean | string;
  custom?: (value: any) => boolean | string;
}

export interface FormFieldState {
  value: any;
  error?: string;
  touched: boolean;
  dirty: boolean;
  valid: boolean;
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  dirty: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
}

export interface FormConfig {
  initialValues?: Record<string, any>;
  validationRules?: Record<string, FormValidationRule>;
  onSubmit?: (values: Record<string, any>) => Promise<void> | void;
  onValidate?: (values: Record<string, any>) => Record<string, string>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

// Option types for select components
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
}

export interface SelectGroup {
  label: string;
  options: SelectOption[];
}

// File upload types
export interface FileUploadConfig {
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  maxFiles?: number;
  allowedTypes?: string[];
}

export interface UploadedFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  url?: string;
  progress?: number;
  error?: string;
}

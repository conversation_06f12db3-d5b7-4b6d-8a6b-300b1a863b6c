import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface AppData {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  gradient?: boolean;
  category?: string;
  isActive?: boolean;
  version?: string;
  createdAt?: string;
  updatedAt?: string;
  permissions?: string[];
  navLinks?: Array<{
    label: string;
    href: string;
    isActive?: boolean;
    icon?: string;
    permissions?: string[];
  }>;
  views?: Record<
    string,
    {
      title: string;
      content: string;
      permissions?: string[];
    }
  >;
  settings?: {
    enableNotifications?: boolean;
    enableAnalytics?: boolean;
    maxUsers?: number;
    features?: string[];
  };
}

interface AppsDataState {
  apps: AppData[];
  currentApp: AppData | null;
  isLoading: boolean;
  error?: string;

  // API Actions
  fetchApps: () => Promise<void>;
  fetchApp: (id: string) => Promise<AppData | null>;
  createApp: (app: Omit<AppData, 'id'>) => Promise<AppData | null>;
  updateApp: (id: string, updates: Partial<AppData>) => Promise<AppData | null>;
  deleteApp: (id: string) => Promise<boolean>;

  // Local Actions
  setApps: (apps: AppData[]) => void;
  setCurrentApp: (app: AppData) => void;
  setLoading: (loading: boolean) => void;
  setError: (error?: string) => void;
  clearError: () => void;

  // Utility functions
  getAppById: (id: string) => AppData | undefined;
  getActiveApps: () => AppData[];
  getAppsByCategory: (category: string) => AppData[];
  searchApps: (query: string) => AppData[];
}

// Helper function to get auth token
const getAuthToken = (): string | null => {
  return localStorage.getItem('authToken');
};

// Helper function to make authenticated API calls
const apiCall = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  const token = getAuthToken();
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

export const useAppsDataStore = create<AppsDataState>()(
  devtools(
    persist(
      (set, get) => ({
        apps: [],
        currentApp: null,
        isLoading: false,
        error: undefined,

        // API Actions
        fetchApps: async () => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall('/api/apps');
            if (!response.ok) {
              throw new Error('Failed to fetch apps');
            }
            const result = await response.json();
            const apps = result.data || result; // Handle paginated response
            set({ apps, isLoading: false });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
          }
        },

        fetchApp: async (id: string) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall(`/api/apps/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch app');
            }
            const app = await response.json();

            // Update the app in the list
            set(state => ({
              apps: state.apps.map(a => (a.id === id ? app : a)),
              isLoading: false,
            }));

            return app;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return null;
          }
        },

        createApp: async (appData: Omit<AppData, 'id'>) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall('/api/apps', {
              method: 'POST',
              body: JSON.stringify(appData),
            });
            if (!response.ok) {
              throw new Error('Failed to create app');
            }
            const newApp = await response.json();

            set(state => ({
              apps: [...state.apps, newApp],
              isLoading: false,
            }));

            return newApp;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return null;
          }
        },

        updateApp: async (id: string, updates: Partial<AppData>) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall(`/api/apps/${id}`, {
              method: 'PUT',
              body: JSON.stringify(updates),
            });
            if (!response.ok) {
              throw new Error('Failed to update app');
            }
            const updatedApp = await response.json();

            set(state => ({
              apps: state.apps.map(app => (app.id === id ? updatedApp : app)),
              currentApp:
                state.currentApp?.id === id ? updatedApp : state.currentApp,
              isLoading: false,
            }));

            return updatedApp;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return null;
          }
        },

        deleteApp: async (id: string) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall(`/api/apps/${id}`, {
              method: 'DELETE',
            });
            if (!response.ok) {
              throw new Error('Failed to delete app');
            }

            set(state => ({
              apps: state.apps.filter(app => app.id !== id),
              currentApp: state.currentApp?.id === id ? null : state.currentApp,
              isLoading: false,
            }));

            return true;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return false;
          }
        },

        // Local Actions
        setApps: (apps: AppData[]) => {
          set({ apps });
        },

        setCurrentApp: (app: AppData) => {
          set({ currentApp: app });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error?: string) => {
          set({ error });
        },

        clearError: () => {
          set({ error: undefined });
        },

        // Utility functions
        getAppById: (id: string) => {
          return get().apps.find(app => app.id === id);
        },

        getActiveApps: () => {
          return get().apps.filter(app => app.isActive !== false);
        },

        getAppsByCategory: (category: string) => {
          return get().apps.filter(app => app.category === category);
        },

        searchApps: (query: string) => {
          const lowercaseQuery = query.toLowerCase();
          return get().apps.filter(
            app =>
              app.title.toLowerCase().includes(lowercaseQuery) ||
              app.description.toLowerCase().includes(lowercaseQuery) ||
              app.category?.toLowerCase().includes(lowercaseQuery)
          );
        },
      }),
      {
        name: 'apps-data-store',
        partialize: state => ({
          apps: state.apps,
          currentApp: state.currentApp,
        }),
      }
    ),
    {
      name: 'apps-data-store',
    }
  )
);

// Helper functions
export const getAppById = (id: string): AppData | undefined => {
  return useAppsDataStore.getState().getAppById(id);
};

export const getActiveApps = (): AppData[] => {
  return useAppsDataStore.getState().getActiveApps();
};

export const getAppsByCategory = (category: string): AppData[] => {
  return useAppsDataStore.getState().getAppsByCategory(category);
};

export const searchApps = (query: string): AppData[] => {
  return useAppsDataStore.getState().searchApps(query);
};

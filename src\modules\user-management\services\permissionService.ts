// Permission Service - API operations for permission management
import type { Permission } from '../types';

export interface PermissionServiceConfig {
  baseUrl?: string;
  timeout?: number;
}

class PermissionService {
  private baseUrl: string;
  private timeout: number;

  constructor(config: PermissionServiceConfig = {}) {
    this.baseUrl = config.baseUrl || '/api';
    this.timeout = config.timeout || 5000;
  }

  /**
   * Fetch all permissions
   */
  async getPermissions(): Promise<Permission[]> {
    const response = await fetch(`${this.baseUrl}/permissions`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch permissions: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Get permissions by resource
   */
  async getPermissionsByResource(resource: string): Promise<Permission[]> {
    const response = await fetch(`${this.baseUrl}/permissions?resource=${resource}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch permissions: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Get a single permission by ID
   */
  async getPermissionById(id: string): Promise<Permission> {
    const response = await fetch(`${this.baseUrl}/permissions/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch permission: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Create a new permission
   */
  async createPermission(permissionData: Omit<Permission, 'id'>): Promise<Permission> {
    const response = await fetch(`${this.baseUrl}/permissions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(permissionData),
    });

    if (!response.ok) {
      throw new Error(`Failed to create permission: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Update an existing permission
   */
  async updatePermission(id: string, permissionData: Partial<Omit<Permission, 'id'>>): Promise<Permission> {
    const response = await fetch(`${this.baseUrl}/permissions/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(permissionData),
    });

    if (!response.ok) {
      throw new Error(`Failed to update permission: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Delete a permission
   */
  async deletePermission(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/permissions/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete permission: ${response.statusText}`);
    }
  }
}

// Export singleton instance
export const permissionService = new PermissionService();

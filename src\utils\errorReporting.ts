/**
 * Error Reporting and Logging System
 * Provides comprehensive error logging, reporting, and analytics
 */

import type { AppError } from './errorTypes';
import { ErrorSeverity } from './errorTypes';

// Error reporting configuration
export interface ErrorReportingConfig {
  enableConsoleLogging: boolean;
  enableRemoteReporting: boolean;
  enableUserFeedback: boolean;
  maxErrorsPerSession: number;
  reportingEndpoint?: string;
  apiKey?: string;
  environment: string;
  userId?: string;
  sessionId: string;
}

// Error report structure
export interface ErrorReport {
  id: string;
  error: AppError;
  userAgent: string;
  url: string;
  timestamp: Date;
  userId?: string;
  sessionId: string;
  environment: string;
  buildVersion: string;
  additionalContext?: Record<string, any>;
  userFeedback?: string;
  reproductionSteps?: string[];
}

// Error reporting service
class ErrorReportingService {
  private config: ErrorReportingConfig;
  private errorCount: number = 0;
  private reportedErrors: Set<string> = new Set();

  constructor(config: ErrorReportingConfig) {
    this.config = config;
  }

  /**
   * Reports an error with comprehensive logging and optional remote reporting
   */
  async reportError(
    error: AppError,
    additionalContext?: Record<string, any>,
    userFeedback?: string
  ): Promise<void> {
    // Check if we should report this error
    if (!this.shouldReportError(error)) {
      return;
    }

    // Create error report
    const report = this.createErrorReport(
      error,
      additionalContext,
      userFeedback
    );

    // Console logging
    if (this.config.enableConsoleLogging) {
      this.logToConsole(report);
    }

    // Remote reporting
    if (this.config.enableRemoteReporting && error.reportable) {
      try {
        await this.sendToRemoteService(report);
      } catch (reportingError) {
        console.error('Failed to send error report:', reportingError);
      }
    }

    // Track reported error
    this.trackReportedError(error);
  }

  /**
   * Creates a comprehensive error report
   */
  private createErrorReport(
    error: AppError,
    additionalContext?: Record<string, any>,
    userFeedback?: string
  ): ErrorReport {
    const reportId = this.generateReportId();

    return {
      id: reportId,
      error,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date(),
      userId: this.config.userId,
      sessionId: this.config.sessionId,
      environment: this.config.environment,
      buildVersion: import.meta.env.VITE_APP_VERSION || 'unknown',
      additionalContext,
      userFeedback,
    };
  }

  /**
   * Logs error to console with appropriate formatting
   */
  private logToConsole(report: ErrorReport): void {
    const { error } = report;

    // Choose console method based on severity
    const logMethod = this.getConsoleMethod(error.severity);

    // Create formatted log message
    const logMessage = `[${error.severity}] ${error.type}: ${error.message}`;

    // Log with grouping for better readability
    console.group(`🚨 Error Report - ${report.id}`);
    logMethod(logMessage);

    if (error.userMessage) {
      console.info('User Message:', error.userMessage);
    }

    if (error.context) {
      console.info('Error Context:', error.context);
    }

    if (error.stack) {
      console.error('Stack Trace:', error.stack);
    }

    console.info('Report Details:', {
      id: report.id,
      timestamp: report.timestamp,
      url: report.url,
      environment: report.environment,
      buildVersion: report.buildVersion,
    });

    if (report.additionalContext) {
      console.info('Additional Context:', report.additionalContext);
    }

    console.groupEnd();
  }

  /**
   * Sends error report to remote service
   */
  private async sendToRemoteService(report: ErrorReport): Promise<void> {
    if (!this.config.reportingEndpoint) {
      return;
    }

    const payload = {
      ...report,
      // Serialize error object properly
      error: {
        name: report.error.name,
        message: report.error.message,
        stack: report.error.stack,
        type: report.error.type,
        severity: report.error.severity,
        userMessage: report.error.userMessage,
        technicalMessage: report.error.technicalMessage,
        recoverable: report.error.recoverable,
        retryable: report.error.retryable,
        reportable: report.error.reportable,
        context: report.error.context,
      },
    };

    const response = await fetch(this.config.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && {
          Authorization: `Bearer ${this.config.apiKey}`,
        }),
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to send error report: ${response.status} ${response.statusText}`
      );
    }
  }

  /**
   * Determines if an error should be reported
   */
  private shouldReportError(error: AppError): boolean {
    // Check error count limit
    if (this.errorCount >= this.config.maxErrorsPerSession) {
      return false;
    }

    // Check if already reported (prevent spam)
    const errorSignature = this.getErrorSignature(error);
    if (this.reportedErrors.has(errorSignature)) {
      return false;
    }

    // Check if error is reportable
    return error.reportable;
  }

  /**
   * Tracks reported errors to prevent duplicates
   */
  private trackReportedError(error: AppError): void {
    this.errorCount++;
    const errorSignature = this.getErrorSignature(error);
    this.reportedErrors.add(errorSignature);
  }

  /**
   * Creates a unique signature for an error
   */
  private getErrorSignature(error: AppError): string {
    return `${error.type}-${error.message}-${error.context?.component || 'unknown'}`;
  }

  /**
   * Gets appropriate console method based on error severity
   */
  private getConsoleMethod(severity: ErrorSeverity): typeof console.log {
    switch (severity) {
      case ErrorSeverity.LOW:
        return console.info;
      case ErrorSeverity.MEDIUM:
        return console.warn;
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return console.error;
      default:
        return console.log;
    }
  }

  /**
   * Generates a unique report ID
   */
  private generateReportId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Updates the configuration
   */
  updateConfig(newConfig: Partial<ErrorReportingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Resets error tracking (useful for new sessions)
   */
  reset(): void {
    this.errorCount = 0;
    this.reportedErrors.clear();
  }

  /**
   * Gets current error statistics
   */
  getStats(): { errorCount: number; reportedErrorTypes: string[] } {
    return {
      errorCount: this.errorCount,
      reportedErrorTypes: Array.from(this.reportedErrors),
    };
  }
}

// Default configuration
const defaultConfig: ErrorReportingConfig = {
  enableConsoleLogging: true,
  enableRemoteReporting: false, // Enable when you have a reporting endpoint
  enableUserFeedback: true,
  maxErrorsPerSession: 50,
  environment: import.meta.env.MODE || 'development',
  sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
};

// Global error reporting service instance
export const errorReportingService = new ErrorReportingService(defaultConfig);

/**
 * Convenience function to report errors
 */
export function reportError(
  error: AppError,
  additionalContext?: Record<string, any>,
  userFeedback?: string
): Promise<void> {
  return errorReportingService.reportError(
    error,
    additionalContext,
    userFeedback
  );
}

/**
 * Configures the error reporting service
 */
export function configureErrorReporting(
  config: Partial<ErrorReportingConfig>
): void {
  errorReportingService.updateConfig(config);
}

/**
 * Gets error reporting statistics
 */
export function getErrorStats(): {
  errorCount: number;
  reportedErrorTypes: string[];
} {
  return errorReportingService.getStats();
}

import '@testing-library/jest-dom';
import { beforeAll, afterEach, afterAll } from 'vitest';
import * as matchers from 'vitest-axe/matchers';
import { expect } from 'vitest';
import 'vitest-axe/extend-expect';

// Extend Vitest matchers with axe accessibility matchers
expect.extend(matchers);

// MSW server instance
let server: any = null;

// Establish API mocking before all tests (if MSW is available)
beforeAll(async () => {
  try {
    // Only import MSW in Node.js environment
    if (typeof window === 'undefined') {
      const { server: mswServer } = await import('../mocks/node');
      server = mswServer;
      server.listen({ onUnhandledRequest: 'error' });
    }
  } catch (error) {
    console.warn('MSW server could not be loaded:', error);
  }
});

// Reset any request handlers that we may add during the tests,
// so they don't affect other tests
afterEach(() => {
  if (server) {
    server.resetHandlers();
  }
});

// Clean up after the tests are finished
afterAll(() => {
  if (server) {
    server.close();
  }
});

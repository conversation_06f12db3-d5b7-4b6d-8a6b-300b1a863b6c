import React from 'react';
import type { ViewModeOption } from './types';

// Common icon components for view modes
export const GridIcon = () => (
  <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
          d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
  </svg>
);

export const ListIcon = () => (
  <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
          d="M4 6h16M4 10h16M4 14h16M4 18h16" />
  </svg>
);

export const KanbanIcon = () => (
  <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
          d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0V17m0-10a2 2 0 012-2h2a2 2 0 012 2v10a2 2 0 01-2 2h-2a2 2 0 01-2-2" />
  </svg>
);

export const CalendarIcon = () => (
  <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

export const TableIcon = () => (
  <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
          d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H5a1 1 0 01-1-1z" />
  </svg>
);

export const CardIcon = () => (
  <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
  </svg>
);

// Preset configurations for common use cases
export const dataViewModes: ViewModeOption[] = [
  {
    id: 'list',
    name: 'List',
    icon: <ListIcon />,
    description: 'Linear list view with detailed information',
    preview: 'Rows of data with full details visible',
  },
  {
    id: 'grid',
    name: 'Grid',
    icon: <GridIcon />,
    description: 'Grid layout with compact tiles',
    preview: 'Compact tiles arranged in a grid',
  },
  {
    id: 'kanban',
    name: 'Kanban',
    icon: <KanbanIcon />,
    description: 'Kanban board with columns and cards',
    preview: 'Cards organized in columns by status',
  },
  {
    id: 'calendar',
    name: 'Calendar',
    icon: <CalendarIcon />,
    description: 'Calendar view for time-based data',
    preview: 'Events displayed on a calendar grid',
  },
];

export const simpleViewModes: ViewModeOption<'grid' | 'kanban'>[] = [
  {
    id: 'grid',
    name: 'Grid',
    icon: <GridIcon />,
    description: 'Compact tiles with icons only',
  },
  {
    id: 'kanban',
    name: 'Kanban',
    icon: <KanbanIcon />,
    description: 'Detailed tiles with descriptions',
  },
];



export const tableViewModes: ViewModeOption[] = [
  {
    id: 'table',
    name: 'Table',
    icon: <TableIcon />,
    description: 'Traditional table with rows and columns',
  },
  {
    id: 'card',
    name: 'Cards',
    icon: <CardIcon />,
    description: 'Card-based layout with rich content',
  },
  {
    id: 'list',
    name: 'List',
    icon: <ListIcon />,
    description: 'Simple list view',
  },
];

import React, { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { WhatsAppIcon, MessageIcon, PhoneIcon, CheckIcon } from '../icons';

export interface VerificationMethod {
  id: 'whatsapp' | 'sms' | 'call';
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  hoverColor: string;
  available?: boolean;
}

export interface VerificationMethodButtonsProps {
  selectedMethod?: 'whatsapp' | 'sms' | 'call';
  onMethodSelect: (method: 'whatsapp' | 'sms' | 'call') => void;
  phoneNumber?: string;
  disabled?: boolean;
  loading?: boolean;
  layout?: 'horizontal' | 'vertical' | 'grid';
  showDescriptions?: boolean;
  className?: string;
  'data-testid'?: string;
}

const verificationMethods: VerificationMethod[] = [
  {
    id: 'whatsapp',
    label: 'WhatsApp',
    description: 'Secure verification via WhatsApp',
    color: '#25D366',
    hoverColor: '#1DA851',
    icon: <WhatsAppIcon className="w-6 h-6" />,
    available: true,
  },
  {
    id: 'sms',
    label: 'SMS',
    description: 'Text message verification',
    color: '#2563eb',
    hoverColor: '#1d4ed8',
    icon: <MessageIcon className="w-6 h-6" />,
    available: true,
  },
  {
    id: 'call',
    label: 'Voice Call',
    description: 'Automated voice verification',
    color: '#059669',
    hoverColor: '#047857',
    icon: <PhoneIcon className="w-6 h-6" />,
    available: true,
  },
];

export function VerificationMethodButtons({
  selectedMethod,
  onMethodSelect,
  phoneNumber,
  disabled = false,
  loading = false,
  layout = 'vertical',
  showDescriptions = true,
  className = '',
  'data-testid': testId,
}: VerificationMethodButtonsProps) {
  const { colors } = useThemeStore();
  const [hoveredMethod, setHoveredMethod] = useState<string | null>(null);

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-row gap-3';
      case 'grid':
        return 'grid grid-cols-1 sm:grid-cols-3 gap-3';
      default:
        return 'flex flex-col gap-3';
    }
  };

  const getButtonClasses = (
    _method: VerificationMethod,
    isSelected: boolean
  ) => {
    const baseClasses = `
      relative group transition-all duration-200 ease-out
      rounded-xl border-2 p-4 cursor-pointer
      ${layout === 'horizontal' ? 'flex-1' : 'w-full'}
      ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-[1.02] active:scale-[0.98]'}
      ${isSelected ? 'ring-2 ring-offset-2' : ''}
    `;

    return baseClasses;
  };

  const handleMethodSelect = (method: VerificationMethod) => {
    if (disabled || loading || !method.available) return;
    onMethodSelect(method.id);
  };

  return (
    <div className={`${className}`} data-testid={testId}>
      {phoneNumber && (
        <div className="mb-4 p-3 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-slate-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
            <span className="text-sm text-slate-600 dark:text-slate-400">
              Verification will be sent to:{' '}
              <strong className="text-slate-900 dark:text-slate-100">
                {phoneNumber}
              </strong>
            </span>
          </div>
        </div>
      )}

      <div className={getLayoutClasses()}>
        {verificationMethods.map(method => {
          const isSelected = selectedMethod === method.id;
          const isHovered = hoveredMethod === method.id;

          return (
            <button
              key={method.id}
              onClick={() => handleMethodSelect(method)}
              onMouseEnter={() => setHoveredMethod(method.id)}
              onMouseLeave={() => setHoveredMethod(null)}
              disabled={disabled || loading || !method.available}
              className={getButtonClasses(method, isSelected)}
              style={{
                backgroundColor: isSelected
                  ? `${method.color}10`
                  : isHovered
                    ? `${method.color}05`
                    : colors.surface,
                borderColor: isSelected
                  ? method.color
                  : isHovered
                    ? `${method.color}60`
                    : colors.border,
              }}
              data-testid={`verification-method-${method.id}`}
            >
              <div className="flex items-center gap-3">
                {/* Icon */}
                <div
                  className={`
                    flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center
                    transition-all duration-200
                    ${isSelected ? 'scale-110' : isHovered ? 'scale-105' : ''}
                  `}
                  style={{
                    backgroundColor:
                      isSelected || isHovered
                        ? method.color
                        : `${method.color}20`,
                    color: isSelected || isHovered ? 'white' : method.color,
                  }}
                >
                  {method.icon}
                </div>

                {/* Content */}
                <div className="flex-1 text-left">
                  <div className="flex items-center gap-2">
                    <h3
                      className="font-semibold transition-colors duration-200"
                      style={{
                        color: isSelected ? method.color : colors.text,
                      }}
                    >
                      {method.label}
                    </h3>
                    {!method.available && (
                      <span className="text-xs px-2 py-1 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-500">
                        Soon
                      </span>
                    )}
                  </div>

                  {showDescriptions && (
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      {method.description}
                    </p>
                  )}
                </div>

                {/* Selection indicator */}
                {isSelected && (
                  <div className="flex-shrink-0">
                    <div
                      className="w-6 h-6 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: method.color }}
                    >
                      <CheckIcon className="w-4 h-4 text-white" />
                    </div>
                  </div>
                )}
              </div>

              {/* Loading indicator */}
              {loading && isSelected && (
                <div className="absolute inset-0 bg-white/50 dark:bg-slate-900/50 rounded-xl flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-slate-300 border-t-blue-600 rounded-full animate-spin" />
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Helper text */}
      <div className="mt-4 text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">
          Choose your preferred verification method
        </p>
      </div>
    </div>
  );
}

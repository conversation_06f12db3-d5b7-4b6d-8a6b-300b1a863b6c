import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { LoginScreen } from './LoginScreen';

// Mock the theme store
vi.mock('../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#2563eb',
      secondary: '#4f46e5',
      accent: '#7c3aed',
      error: '#dc2626',
      success: '#059669',
      background: '#ffffff',
      surface: '#f9fafb',
      text: '#111827',
      border: '#e5e7eb',
    },
  }),
}));

// Mock child components to focus on LoginScreen logic
vi.mock('./LoginForm', () => ({
  LoginForm: ({ onSubmit, loading, error }: any) => (
    <div data-testid="login-form">
      <button
        onClick={() =>
          onSubmit?.({ email: '<EMAIL>', password: 'password' })
        }
      >
        Submit Login
      </button>
      {loading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
    </div>
  ),
}));

vi.mock('./OTPForm', () => ({
  OTPForm: ({ onSubmit, onBack }: any) => (
    <div data-testid="otp-form">
      <button
        onClick={() => onSubmit?.({ phone: '+1234567890', otp: '123456' })}
      >
        Submit OTP
      </button>
      <button onClick={onBack}>Back</button>
    </div>
  ),
}));

vi.mock('./ForgotPasswordForm', () => ({
  ForgotPasswordForm: ({ onSubmit, onBack }: any) => (
    <div data-testid="forgot-password-form">
      <button onClick={() => onSubmit?.('<EMAIL>')}>
        Submit Reset
      </button>
      <button onClick={onBack}>Back</button>
    </div>
  ),
}));

vi.mock('./AccessRequestForm', () => ({
  AccessRequestForm: ({ onSubmit, onBack }: any) => (
    <div data-testid="access-request-form">
      <button
        onClick={() =>
          onSubmit?.({ fullName: 'Test User', email: '<EMAIL>' })
        }
      >
        Submit Request
      </button>
      <button onClick={onBack}>Back</button>
    </div>
  ),
}));

vi.mock('./SocialLoginButtons', () => ({
  SocialLoginButtons: ({ onGoogleLogin }: any) => (
    <div data-testid="social-login-buttons">
      <button onClick={onGoogleLogin}>Google Login</button>
    </div>
  ),
  SocialLoginDivider: () => <div data-testid="social-divider">or</div>,
}));

vi.mock('../security/CaptchaComponent', () => ({
  CaptchaComponent: ({ onVerify }: any) => (
    <div data-testid="captcha-component">
      <button onClick={() => onVerify?.('captcha-token')}>
        Verify CAPTCHA
      </button>
    </div>
  ),
}));

vi.mock('../ThemeToggle', () => ({
  ThemeToggle: () => <div data-testid="theme-toggle">Theme Toggle</div>,
}));

describe('LoginScreen', () => {
  const defaultProps = {
    onLogin: vi.fn(),
    onAccessRequest: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders login screen with brand and form sections', () => {
    render(<LoginScreen {...defaultProps} />);

    expect(screen.getByText(/welcome to nexed/i)).toBeInTheDocument();
    expect(screen.getByText(/sign in/i)).toBeInTheDocument();
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.getByTestId('theme-toggle')).toBeInTheDocument();
  });

  it('renders side swap button', () => {
    render(<LoginScreen {...defaultProps} />);

    const swapButton = screen.getByLabelText(/switch layout sides/i);
    expect(swapButton).toBeInTheDocument();
  });

  it('swaps sides when side swap button is clicked', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    const swapButton = screen.getByLabelText(/switch layout sides/i);

    // Initial state - brand should be on left, form on right
    const brandSections = screen.getAllByText(/welcome to nexed/i);
    const formSections = screen.getAllByText(/sign in/i);

    expect(brandSections).toHaveLength(1);
    expect(formSections).toHaveLength(1);

    await user.click(swapButton);

    // After swap, layout should change (tested through animation classes)
    expect(swapButton).toBeDisabled(); // Should be disabled during animation

    // Wait for animation to complete
    await waitFor(
      () => {
        expect(swapButton).not.toBeDisabled();
      },
      { timeout: 500 }
    );
  });

  it('shows login form by default', () => {
    render(<LoginScreen {...defaultProps} />);

    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.queryByTestId('otp-form')).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('forgot-password-form')
    ).not.toBeInTheDocument();
    expect(screen.queryByTestId('access-request-form')).not.toBeInTheDocument();
  });

  it('switches to OTP mode', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    const otpButton = screen.getByText('OTP Login');
    await user.click(otpButton);

    expect(screen.getByTestId('otp-form')).toBeInTheDocument();
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
  });

  it('switches to forgot password mode', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    const forgotButton = screen.getByText('Forgot Password');
    await user.click(forgotButton);

    expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
  });

  it('switches to access request mode', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    const accessButton = screen.getByText('Request Access');
    await user.click(accessButton);

    expect(screen.getByTestId('access-request-form')).toBeInTheDocument();
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
  });

  it('handles login submission', async () => {
    const user = userEvent.setup();
    const onLogin = vi.fn();
    render(<LoginScreen {...defaultProps} onLogin={onLogin} />);

    const submitButton = screen.getByText('Submit Login');
    await user.click(submitButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    // Should call onLogin after delay
    await waitFor(
      () => {
        expect(onLogin).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password',
        });
      },
      { timeout: 2000 }
    );
  });

  it('handles access request submission', async () => {
    const user = userEvent.setup();
    const onAccessRequest = vi.fn();
    render(<LoginScreen {...defaultProps} onAccessRequest={onAccessRequest} />);

    // Switch to access request mode
    const accessButton = screen.getByText('Request Access');
    await user.click(accessButton);

    const submitButton = screen.getByText('Submit Request');
    await user.click(submitButton);

    await waitFor(
      () => {
        expect(onAccessRequest).toHaveBeenCalledWith({
          fullName: 'Test User',
          email: '<EMAIL>',
        });
      },
      { timeout: 2500 }
    );
  });

  it('shows social login buttons in login mode', () => {
    render(<LoginScreen {...defaultProps} />);

    expect(screen.getByTestId('social-login-buttons')).toBeInTheDocument();
    expect(screen.getByTestId('social-divider')).toBeInTheDocument();
  });

  it('handles social login', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    const googleButton = screen.getByText('Google Login');
    await user.click(googleButton);

    // Should handle social login (mocked behavior)
    expect(googleButton).toBeInTheDocument();
  });

  it('shows CAPTCHA after failed login', async () => {
    const user = userEvent.setup();

    // Mock failed login
    const onLogin = vi.fn().mockRejectedValue(new Error('Login failed'));
    render(<LoginScreen {...defaultProps} onLogin={onLogin} />);

    const submitButton = screen.getByText('Submit Login');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/Error: Login failed/)).toBeInTheDocument();
    });
  });

  it('navigates back from other forms', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    // Go to OTP form
    const otpButton = screen.getByText('OTP Login');
    await user.click(otpButton);

    expect(screen.getByTestId('otp-form')).toBeInTheDocument();

    // Go back to login
    const backButton = screen.getByText('Back');
    await user.click(backButton);

    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.queryByTestId('otp-form')).not.toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<LoginScreen {...defaultProps} />);

    const swapButton = screen.getByLabelText(/switch layout sides/i);
    expect(swapButton).toHaveAttribute('aria-label');
    expect(swapButton).toHaveAttribute('title');
  });

  it('applies custom className', () => {
    render(<LoginScreen {...defaultProps} className="custom-class" />);

    const container =
      screen.getByTestId('login-screen') ||
      document.querySelector('.custom-class');
    expect(container).toHaveClass('custom-class');
  });

  it('disables side swap during animation', async () => {
    const user = userEvent.setup();
    render(<LoginScreen {...defaultProps} />);

    const swapButton = screen.getByLabelText(/switch layout sides/i);

    await user.click(swapButton);
    expect(swapButton).toBeDisabled();

    // Try to click again while disabled
    await user.click(swapButton);

    // Should still be disabled
    expect(swapButton).toBeDisabled();
  });
});

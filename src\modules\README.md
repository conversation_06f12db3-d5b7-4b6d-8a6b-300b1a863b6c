# Modules Directory

This directory contains feature-based modules that encapsulate complete business functionality. Each module is self-contained with its own components, services, types, hooks, and utilities.

## 🏗️ Module Architecture

Each module follows a consistent structure:

```
src/modules/[module-name]/
├── [ModuleName]Module.tsx    # Main module component (entry point)
├── index.ts                  # Module exports
├── README.md                 # Module documentation
├── components/               # React components
│   ├── layout/              # Layout components specific to this module
│   ├── views/               # Main view components (pages)
│   ├── core/                # Core business logic components
│   ├── ui/                  # Module-specific UI components
│   └── index.ts             # Component exports
├── services/                # API services and business logic
│   ├── api.ts               # API service functions
│   ├── websocket.ts         # WebSocket connections (if needed)
│   └── index.ts             # Service exports
├── hooks/                   # Custom React hooks
│   ├── use[Module].ts       # Main module hook
│   ├── use[Feature].ts      # Feature-specific hooks
│   ├── types.ts             # Hook return types
│   └── index.ts             # Hook exports
├── types/                   # TypeScript type definitions
│   ├── index.ts             # Main types export
│   ├── api.ts               # API-related types
│   └── ui.ts                # UI-related types
├── utils/                   # Utility functions
│   ├── helpers.ts           # General helper functions
│   ├── validators.ts        # Validation functions
│   └── index.ts             # Utility exports
└── __tests__/               # Test files
    ├── components/          # Component tests
    ├── hooks/               # Hook tests
    ├── services/            # Service tests
    └── utils/               # Utility tests
```

## 📋 Current Modules

### ✅ Implemented Modules

1. **user-management** - User administration and management
   - User CRUD operations, role management
   - Permission handling, user profiles
   - Optimistic updates and form handling

### 🚧 Planned Modules

3. **sales** - Sales management and CRM
   - Customer management, sales orders
   - Product catalog, pricing
   - Sales reports and analytics

4. **inventory** - Inventory and warehouse management
   - Stock tracking, product management
   - Warehouse operations, transfers
   - Inventory reports and analytics

5. **accounting** - Financial management
   - Invoicing, payments, expenses
   - Financial reports, tax management
   - Chart of accounts, journal entries

6. **hr** - Human resources management
   - Employee management, payroll
   - Time tracking, leave management
   - Performance reviews, recruitment

7. **projects** - Project management and collaboration
   - Task management, project planning
   - Team collaboration, time tracking
   - Project reports and analytics

## 🎯 Module Development Guidelines

### 1. Module Component Structure

Main module component should:
- Accept standard props (className, data-testid)
- Handle routing and view switching
- Integrate with existing layout system
- Use theme store for consistent styling

```typescript
// YourModule.tsx
import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../../components/layout/DynamicAppHeader';

export interface YourModuleProps {
  className?: string;
  'data-testid'?: string;
}

const YourModule: React.FC<YourModuleProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const view = searchParams.get('view') || 'dashboard';
  
  // Module implementation
  
  return (
    <div className={className} data-testid={testId}>
      {/* Module content */}
    </div>
  );
};

export default YourModule;
```

### 3. Service Layer

Services should:
- Use consistent API patterns
- Handle errors appropriately
- Support optimistic updates where applicable
- Include proper TypeScript types

```typescript
// services/api.ts
export class YourModuleService {
  async getItems(): Promise<Item[]> {
    const response = await fetch('/api/your-module/items');
    if (!response.ok) throw new Error('Failed to fetch items');
    return response.json();
  }
}
```

### 4. Custom Hooks

Hooks should:
- Follow React hooks conventions
- Provide consistent return types
- Handle loading and error states
- Support data mutations

```typescript
// hooks/useYourModule.ts
export function useYourModule(): UseYourModuleReturn {
  // Hook implementation
}
```

### 5. Type Definitions

Types should:
- Be well-documented
- Follow consistent naming conventions
- Include all necessary properties
- Support extensibility

```typescript
// types/index.ts
export interface YourModuleItem {
  id: string;
  name: string;
  // ... other properties
}
```

## 🧪 Testing Strategy

Each module should include:
- Component tests with React Testing Library
- Hook tests with @testing-library/react-hooks
- Service tests with MSW mocking
- Integration tests for key workflows
- Accessibility tests with vitest-axe

## 📦 Module Exports

Each module should export:
- Main module component (default export)
- All public components, hooks, types, and utilities
- Clear TypeScript types for all exports

```typescript
// index.ts
export { default as YourModule } from './YourModule';
export type { YourModuleProps } from './YourModule';

export * from './components';
export * from './hooks';
export * from './types';
export * from './services';
export * from './utils';
```

## 🔄 Migration from Components

When converting existing components to modules:
1. Create module structure
2. Move related components to module
3. Update import paths
4. Update tests and documentation
5. Remove old component files

## 🎨 Design System Integration

All modules must:
- Use theme store for colors and styling
- Follow existing design patterns
- Support dark/light mode
- Be responsive and accessible
- Use consistent spacing and typography

## 📚 Documentation

Each module should include:
- README.md with feature overview
- Component documentation
- API documentation
- Usage examples
- Migration guides (if applicable)

/**
 * Nexed Web Application Configuration
 * TypeScript configuration file for the application
 * Values can be overridden by environment variables or runtime configuration
 */

export interface AppConfig {
  app: {
    name: string;
    version: string;
    description: string;
    environment: 'development' | 'staging' | 'production';
  };
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
    retryDelay: number;
  };
  features: {
    // Core Features
    enableAnalytics: boolean;
    enableNotifications: boolean;
    enableDarkMode: boolean;
    enableRealTimeUpdates: boolean;
    // Advanced Features
    enableAdvancedSearch: boolean;
    enableBetaFeatures: boolean;
    enableExperimentalUI: boolean;
    enableDebugMode: boolean;
    // Enterprise Features
    enableSSOLogin: boolean;
    enableAuditLogging: boolean;
    enableDataExport: boolean;
    enableAdvancedReporting: boolean;
  };
  ui: {
    // Theme Configuration
    defaultTheme: 'light' | 'dark' | 'system';
    allowThemeToggle: boolean;
    // Layout Settings
    sidebarCollapsed: boolean;
    compactMode: boolean;
    showTooltips: boolean;
    animationsEnabled: boolean;
    reducedMotion: boolean;
    highContrastMode: boolean;
    // Data Display
    itemsPerPage: number;
    maxItemsPerPage: number;
    enableVirtualScrolling: boolean;
    // Accessibility
    enableScreenReader: boolean;
    enableKeyboardNavigation: boolean;
    focusIndicatorVisible: boolean;
  };
  performance: {
    // Caching
    enableCaching: boolean;
    cacheTimeout: number;
    // Loading
    enableLazyLoading: boolean;
    enableCodeSplitting: boolean;
    preloadCriticalResources: boolean;
    // Optimization
    enableImageOptimization: boolean;
    enableAssetCompression: boolean;
  };
  security: {
    // Session Management
    sessionTimeout: number;
    enableAutoLogout: boolean;
    maxLoginAttempts: number;
    lockoutDuration: number;
    // Content Security
    enableCSP: boolean;
    enableXSSProtection: boolean;
    enableClickjacking: boolean;
    // Data Protection
    enableDataEncryption: boolean;
    enableSecureHeaders: boolean;
  };
  uploads: {
    maxFileSize: number;
    allowedTypes: string[];
    enableVirusScanning: boolean;
    enableImageResizing: boolean;
  };
  localization: {
    defaultLanguage: string;
    supportedLanguages: string[];
    enableRTL: boolean;
    dateFormat: string;
    timeFormat: string;
    timezone: string;
    numberFormat: string;
    currencyFormat: string;
  };
  notifications: {
    // Types
    enableToast: boolean;
    enableEmail: boolean;
    enablePush: boolean;
    enableInApp: boolean;
    // Behavior
    defaultDuration: number;
    maxNotifications: number;
    enableSound: boolean;
    enableVibration: boolean;
    // Positioning
    position: string;
  };
  analytics: {
    enableTracking: boolean;
    provider: string;
    trackPageViews: boolean;
    trackUserInteractions: boolean;
    trackErrors: boolean;
    enableHeatmaps: boolean;
    enableSessionRecording: boolean;
  };
  development: {
    enableHotReload: boolean;
    enableSourceMaps: boolean;
    enableProfiling: boolean;
    enableStorybook: boolean;
    enableTestMode: boolean;
    mockApiResponses: boolean;
    enableDevLogin: boolean;
  };
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    enableConsoleLogging: boolean;
    enableFileLogging: boolean;
    enableRemoteLogging: boolean;
    maxLogSize: number;
    logRetentionDays: number;
  };
  integrations: {
    // External Services
    enableGoogleMaps: boolean;
    enableStripePayments: boolean;
    enableSlackNotifications: boolean;
    enableZendeskSupport: boolean;
    // Social Login
    enableGoogleLogin: boolean;
    enableMicrosoftLogin: boolean;
    enableGitHubLogin: boolean;
  };
  backup: {
    enableAutoBackup: boolean;
    backupInterval: number;
    retentionPeriod: number;
    enableCloudBackup: boolean;
  };
  monitoring: {
    enableHealthChecks: boolean;
    healthCheckInterval: number;
    enablePerformanceMonitoring: boolean;
    enableErrorTracking: boolean;
    enableUptimeMonitoring: boolean;
  };
}

/**
 * Default application configuration
 */
export const defaultConfig: AppConfig = {
  // Application Information
  app: {
    name: 'Nexed Web',
    version: '1.0.0',
    description: 'Professional Enterprise Web Application',
    environment: 'development',
  },

  // API Configuration
  api: {
    baseUrl: '/api',
    timeout: 30000, // 30 seconds
    retries: 3,
    retryDelay: 1000, // 1 second
  },

  // Feature Flags
  features: {
    // Core Features
    enableAnalytics: true,
    enableNotifications: true,
    enableDarkMode: true,
    enableRealTimeUpdates: true,
    // Advanced Features
    enableAdvancedSearch: true,
    enableBetaFeatures: false,
    enableExperimentalUI: false,
    enableDebugMode: false,
    // Enterprise Features
    enableSSOLogin: false,
    enableAuditLogging: true,
    enableDataExport: true,
    enableAdvancedReporting: false,
  },

  // User Interface Settings
  ui: {
    // Theme Configuration
    defaultTheme: 'system',
    allowThemeToggle: true,
    // Layout Settings
    sidebarCollapsed: false,
    compactMode: false,
    showTooltips: true,
    animationsEnabled: true,
    reducedMotion: false,
    highContrastMode: false,
    // Data Display
    itemsPerPage: 25,
    maxItemsPerPage: 100,
    enableVirtualScrolling: true,
    // Accessibility
    enableScreenReader: true,
    enableKeyboardNavigation: true,
    focusIndicatorVisible: true,
  },

  // Performance Settings
  performance: {
    // Caching
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    // Loading
    enableLazyLoading: true,
    enableCodeSplitting: true,
    preloadCriticalResources: true,
    // Optimization
    enableImageOptimization: true,
    enableAssetCompression: true,
  },

  // Security Settings
  security: {
    // Session Management
    sessionTimeout: 3600000, // 1 hour
    enableAutoLogout: true,
    maxLoginAttempts: 5,
    lockoutDuration: 900000, // 15 minutes
    // Content Security
    enableCSP: true,
    enableXSSProtection: true,
    enableClickjacking: true,
    // Data Protection
    enableDataEncryption: true,
    enableSecureHeaders: true,
  },

  // File Upload Settings
  uploads: {
    maxFileSize: 10485760, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
    enableVirusScanning: false,
    enableImageResizing: true,
  },

  // Localization Settings
  localization: {
    defaultLanguage: 'en',
    supportedLanguages: ['en', 'es', 'fr', 'de'],
    enableRTL: false,
    dateFormat: 'MM/dd/yyyy',
    timeFormat: '12h',
    timezone: 'UTC',
    numberFormat: 'en-US',
    currencyFormat: 'USD',
  },

  // Notification Settings
  notifications: {
    // Types
    enableToast: true,
    enableEmail: false,
    enablePush: false,
    enableInApp: true,
    // Behavior
    defaultDuration: 5000, // 5 seconds
    maxNotifications: 5,
    enableSound: true,
    enableVibration: false,
    // Positioning
    position: 'top-right',
  },

  // Analytics Settings
  analytics: {
    enableTracking: false,
    provider: 'none',
    trackPageViews: true,
    trackUserInteractions: true,
    trackErrors: true,
    enableHeatmaps: false,
    enableSessionRecording: false,
  },

  // Development Settings
  development: {
    enableHotReload: true,
    enableSourceMaps: true,
    enableProfiling: false,
    enableStorybook: true,
    enableTestMode: false,
    mockApiResponses: false,
    enableDevLogin: true,
  },

  // Logging Settings
  logging: {
    level: 'info',
    enableConsoleLogging: true,
    enableFileLogging: false,
    enableRemoteLogging: false,
    maxLogSize: 10485760, // 10MB
    logRetentionDays: 30,
  },

  // Integration Settings
  integrations: {
    // External Services
    enableGoogleMaps: false,
    enableStripePayments: false,
    enableSlackNotifications: false,
    enableZendeskSupport: false,
    // Social Login
    enableGoogleLogin: false,
    enableMicrosoftLogin: false,
    enableGitHubLogin: false,
  },

  // Backup and Recovery
  backup: {
    enableAutoBackup: false,
    backupInterval: 86400000, // 24 hours
    retentionPeriod: **********, // 30 days
    enableCloudBackup: false,
  },

  // Monitoring and Health
  monitoring: {
    enableHealthChecks: true,
    healthCheckInterval: 60000, // 1 minute
    enablePerformanceMonitoring: false,
    enableErrorTracking: false,
    enableUptimeMonitoring: false,
  },
};

/**
 * Environment-specific configuration overrides
 */
export function getEnvironmentConfig(environment: string): Partial<AppConfig> {
  switch (environment) {
    case 'development':
      return {
        features: {
          enableAnalytics: false,
          enableNotifications: true,
          enableDarkMode: true,
          enableRealTimeUpdates: true,
          enableAdvancedSearch: true,
          enableBetaFeatures: true,
          enableExperimentalUI: true,
          enableDebugMode: true,
          enableSSOLogin: false,
          enableAuditLogging: true,
          enableDataExport: true,
          enableAdvancedReporting: false,
        },
        development: {
          enableHotReload: true,
          enableSourceMaps: true,
          enableProfiling: false,
          enableStorybook: true,
          enableTestMode: true,
          mockApiResponses: true,
          enableDevLogin: true,
        },
        logging: {
          level: 'debug',
          enableConsoleLogging: true,
          enableFileLogging: false,
          enableRemoteLogging: false,
          maxLogSize: 10485760, // 10MB
          logRetentionDays: 7,
        },
      };

    case 'staging':
      return {
        features: {
          enableAnalytics: true,
          enableNotifications: true,
          enableDarkMode: true,
          enableRealTimeUpdates: true,
          enableAdvancedSearch: true,
          enableBetaFeatures: true,
          enableExperimentalUI: false,
          enableDebugMode: false,
          enableSSOLogin: true,
          enableAuditLogging: true,
          enableDataExport: true,
          enableAdvancedReporting: true,
        },
        development: {
          enableHotReload: false,
          enableSourceMaps: true,
          enableProfiling: false,
          enableStorybook: false,
          enableTestMode: false,
          mockApiResponses: false,
          enableDevLogin: false,
        },
        logging: {
          level: 'info',
          enableConsoleLogging: true,
          enableFileLogging: true,
          enableRemoteLogging: true,
          maxLogSize: 52428800, // 50MB
          logRetentionDays: 30,
        },
      };

    case 'production':
      return {
        features: {
          enableAnalytics: true,
          enableNotifications: true,
          enableDarkMode: true,
          enableRealTimeUpdates: true,
          enableAdvancedSearch: true,
          enableBetaFeatures: false,
          enableExperimentalUI: false,
          enableDebugMode: false,
          enableSSOLogin: true,
          enableAuditLogging: true,
          enableDataExport: true,
          enableAdvancedReporting: true,
        },
        development: {
          enableHotReload: false,
          enableSourceMaps: false,
          enableProfiling: false,
          enableStorybook: false,
          enableTestMode: false,
          mockApiResponses: false,
          enableDevLogin: false,
        },
        logging: {
          level: 'warn',
          enableConsoleLogging: false,
          enableFileLogging: true,
          enableRemoteLogging: true,
          maxLogSize: 104857600, // 100MB
          logRetentionDays: 90,
        },
      };

    default:
      return {};
  }
}

/**
 * Deep merge configuration objects
 */
export function mergeConfig(
  base: AppConfig,
  override: Partial<AppConfig>
): AppConfig {
  const result = { ...base };

  for (const key in override) {
    const value = override[key as keyof AppConfig];
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key as keyof AppConfig] = {
        ...result[key as keyof AppConfig],
        ...value,
      } as any;
    } else if (value !== undefined) {
      result[key as keyof AppConfig] = value as any;
    }
  }

  return result;
}

/**
 * Get the final configuration with environment overrides
 */
export function getAppConfig(): AppConfig {
  const environment = import.meta.env.MODE || 'development';
  const envOverrides = getEnvironmentConfig(environment);
  return mergeConfig(defaultConfig, envOverrides);
}

# Views System

A comprehensive collection of view components for displaying and interacting with data in various formats. This system provides 12+ different view types suitable for enterprise applications.

## Available Views

### 📊 Data Display Views

#### List View

- **Purpose**: Tabular data display with sorting, filtering, and selection
- **Use Cases**: User lists, transaction records, inventory items
- **Features**: Column sorting, row selection, pagination, custom cell rendering

#### Grid View

- **Purpose**: Card-based layout in responsive grid
- **Use Cases**: Product catalogs, image galleries, dashboard tiles
- **Features**: Responsive columns, item selection, custom item rendering

#### Card View

- **Purpose**: Rich card-based display with structured fields
- **Use Cases**: Contact cards, project summaries, profile displays
- **Features**: Field-based rendering, image support, actions

### 📊 Data Visualization Views

#### Kanban View

- **Purpose**: Workflow visualization with drag-and-drop
- **Use Cases**: Project management, sales pipelines, task tracking
- **Features**: Drag-and-drop, column limits, swimlanes, custom cards

#### Calendar View

- **Purpose**: Time-based event visualization
- **Use Cases**: Scheduling, deadlines, project milestones
- **Features**: Month/week/day views, event colors, time slots

#### Pivot View

- **Purpose**: Data aggregation and cross-tabulation
- **Use Cases**: Sales analysis, financial reporting, data exploration
- **Features**: Multi-dimensional grouping, aggregations, totals

#### Graph View

- **Purpose**: Chart-based data visualization
- **Use Cases**: Trends, comparisons, distributions
- **Features**: Multiple chart types, legends, interactive data points

### 🕒 Activity & Timeline Views

#### Activity View

- **Purpose**: Chronological activity feed
- **Use Cases**: Audit trails, communication logs, system events
- **Features**: Timeline visualization, grouping, activity types

### 🗺️ Spatial & Hierarchical Views

#### Map View

- **Purpose**: Geographic data visualization
- **Use Cases**: Store locations, delivery routes, regional analysis
- **Features**: Markers, clustering, zoom controls, legends

#### Gantt View

- **Purpose**: Project timeline and dependency visualization
- **Use Cases**: Project planning, resource allocation, scheduling
- **Features**: Task bars, dependencies, progress tracking, time scales

#### Hierarchical View

- **Purpose**: Tree-structured data display
- **Use Cases**: Organization charts, file systems, category trees
- **Features**: Expand/collapse, drag-and-drop, custom icons

### 🔗 Relationship & Form Views

#### Relationship View

- **Purpose**: Network graph visualization
- **Use Cases**: Social networks, system dependencies, data relationships
- **Features**: Force-directed layout, zoom/pan, node/edge interactions

#### Form View

- **Purpose**: Single record editing and display
- **Use Cases**: User profiles, settings, data entry
- **Features**: Field validation, sections, multiple layouts

## Usage Examples

### Basic List View

```tsx
import { ListView } from '../components/views';

const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'status', label: 'Status' },
];

<ListView
  data={users}
  columns={columns}
  selectable
  onRowClick={user => console.log('Selected:', user)}
/>;
```

### Kanban Board

```tsx
import { KanbanView } from '../components/views';

const config = {
  type: 'kanban',
  columns: [
    { id: 'todo', title: 'To Do', color: '#3b82f6' },
    { id: 'doing', title: 'In Progress', color: '#f59e0b' },
    { id: 'done', title: 'Done', color: '#10b981' },
  ],
  groupByField: 'status',
  cardFields: ['title', 'assignee'],
};

<KanbanView
  data={tasks}
  config={config}
  onItemMove={(id, from, to) => updateTaskStatus(id, to)}
  renderCard={task => <TaskCard task={task} />}
/>;
```

### Calendar View

```tsx
import { CalendarView } from '../components/views';

const config = {
  type: 'calendar',
  dateField: 'startDate',
  endDateField: 'endDate',
  titleField: 'title',
  viewModes: ['month', 'week', 'day'],
  defaultViewMode: 'month',
};

<CalendarView
  data={events}
  config={config}
  onEventClick={event => openEventDetails(event)}
/>;
```

## State Management

The views system includes a Zustand store for managing view state:

```tsx
import { useViewStore } from '../stores/viewStore';

const {
  currentViewId,
  viewData,
  selectedItems,
  setCurrentView,
  setViewData,
  selectItem,
  addFilter,
} = useViewStore();
```

## Integration with DynamicAppHeader

Views integrate seamlessly with the enhanced DynamicAppHeader:

```tsx
import { DynamicAppHeader } from '../components/layout';
import { transformToEnhancedProps } from '../types/header';

const headerProps = {
  app: { name: 'My App', icon: <Icon />, navLinks: [] },
  user: { name: 'User', avatar: <Avatar />, notifications: [] },
  view: {
    title: 'Data View',
    viewModes: [
      { name: 'List', icon: '📋' },
      { name: 'Kanban', icon: '📊' },
      { name: 'Calendar', icon: '📅' },
    ],
    activeViewMode: 'List',
    // ... other view props
  },
};

<DynamicAppHeader {...headerProps} />;
```

## Customization

### Custom Renderers

Most views support custom rendering functions:

```tsx
// Custom list cell renderer
<ListView
  columns={[
    {
      key: 'status',
      label: 'Status',
      render: (value) => <StatusBadge status={value} />
    }
  ]}
/>

// Custom card renderer
<GridView
  renderItem={(item) => <CustomCard item={item} />}
/>
```

### Theming

Views automatically use the theme system:

```tsx
import { useTheme } from '../hooks/useTheme';

const { colors, isDark } = useTheme();
// Views automatically apply theme colors
```

## Performance Considerations

- **Virtualization**: Large datasets should use virtualized components
- **Pagination**: Use pagination for better performance with large datasets
- **Memoization**: Custom renderers should be memoized when possible
- **Lazy Loading**: Consider lazy loading for complex views

## Accessibility

All views include:

- Keyboard navigation support
- ARIA labels and roles
- Screen reader compatibility
- Focus management
- High contrast support

## Testing

Views include comprehensive test coverage:

```bash
# Run view tests
npm test -- --testPathPattern=views

# Run specific view tests
npm test ListView.test.tsx
```

## Contributing

When adding new views:

1. Create the view component in `src/components/views/[ViewName]/`
2. Add the view configuration to `src/types/views.ts`
3. Export from `src/components/views/index.ts`
4. Add demo to `src/pages/views-demo.tsx`
5. Write tests and documentation
6. Update this README

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 18+
- Zustand (state management)
- TypeScript 4.9+
- Tailwind CSS (styling)

// Feedback Components - Components for user feedback and notifications
// These components provide feedback, alerts, and interactive overlays

// Modals & Overlays
export { Modal } from './Modal';
export type { ModalProps } from './Modal';

// TODO: Implement additional feedback components
// Alerts & Messages
// export { default as Alert } from './Alert/Alert'
// export type { AlertProps } from './Alert/Alert'

// export { default as Toast } from './Toast/Toast'
// export type { ToastProps } from './Toast/Toast'

// export { default as ToastContainer } from './ToastContainer/ToastContainer'
// export type { ToastContainerProps } from './ToastContainer/ToastContainer'

// export { default as Banner } from './Banner/Banner'
// export type { BannerProps } from './Banner/Banner'

// export { default as Dialog } from './Dialog/Dialog'
// export type { DialogProps } from './Dialog/Dialog'

// export { default as Drawer } from './Drawer/Drawer'
// export type { DrawerProps } from './Drawer/Drawer'

// export { default as Popover } from './Popover/Popover'
// export type { PopoverProps } from './Popover/Popover'

// export { default as Tooltip } from './Tooltip/Tooltip'
// export type { TooltipProps } from './Tooltip/Tooltip'

// Progress & Loading
// export { default as ProgressBar } from './ProgressBar/ProgressBar'
// export type { ProgressBarProps } from './ProgressBar/ProgressBar'

// export { default as LoadingOverlay } from './LoadingOverlay/LoadingOverlay'
// export type { LoadingOverlayProps } from './LoadingOverlay/LoadingOverlay'

// Confirmation
// export { default as ConfirmDialog } from './ConfirmDialog/ConfirmDialog'
// export type { ConfirmDialogProps } from './ConfirmDialog/ConfirmDialog'

// Empty States
// export { default as EmptyState } from './EmptyState/EmptyState'
// export type { EmptyStateProps } from './EmptyState/EmptyState'

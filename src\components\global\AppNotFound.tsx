import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface AppNotFoundProps {
  title?: string;
  message?: string;
  className?: string;
  'data-testid'?: string;
}

const AppNotFound: React.FC<AppNotFoundProps> = ({
  title = 'App not found',
  message = 'Redirecting to dashboard...',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div 
      className={`min-h-screen bg-background flex items-center justify-center ${className}`}
      data-testid={testId}
    >
      <div className="text-center">
        <h2
          className="text-xl font-semibold mb-2"
          style={{ color: colors.text }}
        >
          {title}
        </h2>
        <p className="text-sm" style={{ color: colors.textSecondary }}>
          {message}
        </p>
      </div>
    </div>
  );
};

export default AppNotFound;

import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Home Page Component
 *
 * Root page that redirects to the appropriate landing page.
 * Currently redirects to login for authentication.
 */
export default function HomePage() {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to login page
    // In a real app, this could check authentication status first
    navigate('/login', { replace: true });
  }, [navigate]);

  // Show loading state while redirecting
  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-slate-600 dark:text-slate-400">Loading...</p>
      </div>
    </div>
  );
}

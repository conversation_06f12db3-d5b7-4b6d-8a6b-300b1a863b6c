// Common Components - Shared utility components used across the application
// These components provide common functionality and patterns

// Error Handling
export { default as ErrorBoundary } from './ErrorBoundary/ErrorBoundary';
export type {
  ErrorBoundaryProps,
  ErrorFallbackProps,
} from './ErrorBoundary/ErrorBoundary';

export { default as ErrorFallback } from './ErrorFallback/ErrorFallback';
export type { ErrorFallbackProps as ErrorFallbackComponentProps } from './ErrorFallback/ErrorFallback';

// Branding
export { Logo } from './Logo';
export type { LogoProps } from './Logo';

// Loading States
// export { default as LoadingSpinner } from './LoadingSpinner/LoadingSpinner'
// export type { LoadingSpinnerProps } from './LoadingSpinner/LoadingSpinner'

// export { default as PageLoader } from './PageLoader/PageLoader'
// export type { PageLoaderProps } from './PageLoader/PageLoader'

// Conditional Rendering
// export { default as ConditionalRender } from './ConditionalRender/ConditionalRender'
// export type { ConditionalRenderProps } from './ConditionalRender/ConditionalRender'

// export { default as FeatureFlag } from './FeatureFlag/FeatureFlag'
// export type { FeatureFlagProps } from './FeatureFlag/FeatureFlag'

// Accessibility
// export { default as SkipLink } from './SkipLink/SkipLink'
// export type { SkipLinkProps } from './SkipLink/SkipLink'

// export { default as ScreenReaderOnly } from './ScreenReaderOnly/ScreenReaderOnly'
// export type { ScreenReaderOnlyProps } from './ScreenReaderOnly/ScreenReaderOnly'

// Utilities
// export { default as Portal } from './Portal/Portal'
// export type { PortalProps } from './Portal/Portal'

// export { default as FocusTrap } from './FocusTrap/FocusTrap'
// export type { FocusTrapProps } from './FocusTrap/FocusTrap'

// export { default as ClickOutside } from './ClickOutside/ClickOutside'
// export type { ClickOutsideProps } from './ClickOutside/ClickOutside'

// Theme Integration
// export { default as ThemeProvider } from './ThemeProvider/ThemeProvider'
// export type { ThemeProviderProps } from './ThemeProvider/ThemeProvider'

// export { default as ColorModeToggle } from './ColorModeToggle/ColorModeToggle'
// export type { ColorModeToggleProps } from './ColorModeToggle/ColorModeToggle'

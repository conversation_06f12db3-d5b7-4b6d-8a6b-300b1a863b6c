import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import DynamicAppBottomBar from '../DynamicAppBottomBar/DynamicAppBottomBar';
import type { DynamicAppBottomBarProps } from '../DynamicAppBottomBar/DynamicAppBottomBar';
import { cn } from '../../../utils/cn';

// Type Definitions
export interface AppDynamicContentProps {
  view: DynamicAppBottomBarProps['view'];
  children: React.ReactNode;
  className?: string;
  contentClassName?: string;
  'data-testid'?: string;
}

/**
 * AppDynamicContent Component
 * 
 * Combines DynamicAppBottomBar with main content area to provide a uniform UI
 * pattern across the application. This component ensures consistent layout
 * and spacing between the bottom bar and content.
 * 
 * Features:
 * - Integrates DynamicAppBottomBar for contextual controls
 * - Provides consistent content area styling
 * - Maintains responsive behavior
 * - Supports custom content through children prop
 * 
 * Usage:
 * ```tsx
 * <AppDynamicContent view={viewConfig}>
 *   <YourMainContent />
 * </AppDynamicContent>
 * ```
 */
const AppDynamicContent: React.FC<AppDynamicContentProps> = ({
  view,
  children,
  className = '',
  contentClassName = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div
      className={cn('flex flex-col min-h-0', className)}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Dynamic App Bottom Bar - Contextual Controls */}
      <DynamicAppBottomBar view={view} />

      {/* Main Content Area */}
      <main
        className={cn(
          'flex-1 max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8',
          contentClassName
        )}
      >
        {children}
      </main>
    </div>
  );
};

export default AppDynamicContent;

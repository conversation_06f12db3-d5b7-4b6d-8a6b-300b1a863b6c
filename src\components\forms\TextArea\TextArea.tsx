import React, { forwardRef } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import type { FormControlProps } from '../types';

export interface TextAreaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'>,
    FormControlProps {
  rows?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
  maxLength?: number;
  showCharCount?: boolean;
}

/**
 * TextArea component for forms
 * Provides multi-line text input with auto-resize and character count features
 */
export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  (
    {
      label,
      error,
      helperText,
      validationState = 'default',
      size = 'md',
      variant = 'default',
      fullWidth = false,
      rows = 3,
      resize = 'vertical',
      autoResize = false,
      maxLength,
      showCharCount = false,
      value = '',
      onChange,
      className = '',
      disabled = false,
      required = false,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const { colors } = useThemeStore();

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-4 py-3 text-lg',
    };

    const baseTextAreaClasses =
      'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';

    const getValidationColor = () => {
      if (error || validationState === 'error') return colors.error;
      if (validationState === 'warning') return colors.warning;
      if (validationState === 'success') return colors.success;
      return colors.border;
    };

    const getVariantStyles = () => {
      const borderColor = getValidationColor();
      
      switch (variant) {
        case 'filled':
          return {
            backgroundColor: colors.muted,
            borderColor: 'transparent',
            color: colors.text,
            '--focus-ring-color': colors.primary,
          };
        case 'outlined':
          return {
            backgroundColor: 'transparent',
            borderColor,
            color: colors.text,
            '--focus-ring-color': colors.primary,
          };
        default:
          return {
            backgroundColor: colors.input,
            borderColor,
            color: colors.inputForeground,
            '--focus-ring-color': colors.primary,
          };
      }
    };

    const variantStyles = getVariantStyles();

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    const textAreaClasses = cn(
      baseTextAreaClasses,
      sizeClasses[size],
      resizeClasses[resize],
      autoResize && 'resize-none',
      className
    );

    const labelClasses = cn(
      'block text-sm font-medium mb-2',
      required && "after:content-['*'] after:ml-0.5 after:text-red-500"
    );

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      
      // Respect maxLength if set
      if (maxLength && newValue.length > maxLength) {
        return;
      }
      
      onChange?.(newValue);

      // Auto-resize functionality
      if (autoResize && e.target) {
        e.target.style.height = 'auto';
        e.target.style.height = `${e.target.scrollHeight}px`;
      }
    };

    const characterCount = typeof value === 'string' ? value.length : 0;
    const isOverLimit = maxLength && characterCount > maxLength;

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {label && (
          <label
            className={labelClasses}
            style={{ color: error ? colors.error : colors.text }}
          >
            {label}
          </label>
        )}

        <div className="relative">
          <textarea
            ref={ref}
            className={textAreaClasses}
            style={variantStyles}
            disabled={disabled}
            required={required}
            rows={rows}
            value={value}
            onChange={handleChange}
            data-testid={testId}
            {...props}
          />
        </div>

        <div className="mt-1 flex justify-between items-start">
          <div className="flex-1">
            {error && (
              <p className="text-sm" style={{ color: colors.error }}>
                {error}
              </p>
            )}
            {helperText && !error && (
              <p className="text-sm" style={{ color: colors.mutedForeground }}>
                {helperText}
              </p>
            )}
          </div>
          
          {(showCharCount || maxLength) && (
            <div className="flex-shrink-0 ml-2">
              <span
                className="text-xs"
                style={{
                  color: isOverLimit ? colors.error : colors.mutedForeground,
                }}
              >
                {characterCount}
                {maxLength && `/${maxLength}`}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  }
);

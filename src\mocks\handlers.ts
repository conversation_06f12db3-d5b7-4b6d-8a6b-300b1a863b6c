// Comprehensive MSW handlers for Nexed Web
import { authHandlers } from './handlers/auth';
import { userHandlers } from './handlers/users';
import { companyHandlers } from './handlers/companies';
import { appHandlers } from './handlers/apps';
import { configurationHandlers } from './handlers/configuration';

// Combine all handlers
export const handlers = [
  ...authHandlers,
  ...userHandlers,
  ...companyHandlers,
  ...appHandlers,
  ...configurationHandlers,
];

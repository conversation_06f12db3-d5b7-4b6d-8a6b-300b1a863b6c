// User mock data for MSW
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'user' | 'moderator' | 'guest';
  avatar: string;
  permissions: string[];
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  lastLogin?: string;
  preferences?: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    notifications: boolean;
  };
}

export interface AuthCredential {
  email: string;
  password: string;
  userId: string;
}

// Production users
export const users: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: '2024-01-15T10:30:00Z',
    lastLogin: '2024-01-20T09:15:00Z',
    avatar: '👨‍💼',
    permissions: [
      'read',
      'write',
      'delete',
      'admin',
      'manage_users',
      'manage_apps',
    ],
    status: 'active',
    preferences: {
      theme: 'dark',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'manager',
    createdAt: '2024-01-16T14:20:00Z',
    lastLogin: '2024-01-20T08:45:00Z',
    avatar: '👩‍💼',
    permissions: ['read', 'write', 'manage_team', 'view_reports'],
    status: 'active',
    preferences: {
      theme: 'light',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'moderator',
    createdAt: '2024-01-17T09:15:00Z',
    lastLogin: '2024-01-19T16:30:00Z',
    avatar: '👮‍♀️',
    permissions: ['read', 'write', 'moderate'],
    status: 'active',
    preferences: {
      theme: 'system',
      language: 'en',
      notifications: false,
    },
  },
  {
    id: '4',
    name: 'Alice Cooper',
    email: '<EMAIL>',
    role: 'user',
    createdAt: '2024-01-18T11:45:00Z',
    lastLogin: '2024-01-19T14:20:00Z',
    avatar: '👤',
    permissions: ['read', 'write'],
    status: 'active',
    preferences: {
      theme: 'light',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: '5',
    name: 'Charlie Brown',
    email: '<EMAIL>',
    role: 'guest',
    createdAt: '2024-01-19T08:30:00Z',
    avatar: '👻',
    permissions: ['read'],
    status: 'active',
    preferences: {
      theme: 'system',
      language: 'en',
      notifications: false,
    },
  },
];

// Development users for testing
export const devUsers: User[] = [
  {
    id: 'dev-admin',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '👨‍💼',
    permissions: [
      'read',
      'write',
      'delete',
      'admin',
      'manage_users',
      'manage_apps',
    ],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: new Date().toISOString(),
    preferences: {
      theme: 'dark',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: 'dev-user',
    name: 'Regular User',
    email: '<EMAIL>',
    role: 'user',
    avatar: '👤',
    permissions: ['read', 'write'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: new Date().toISOString(),
    preferences: {
      theme: 'light',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: 'dev-moderator',
    name: 'Moderator User',
    email: '<EMAIL>',
    role: 'moderator',
    avatar: '👮‍♀️',
    permissions: ['read', 'write', 'moderate'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: new Date().toISOString(),
    preferences: {
      theme: 'system',
      language: 'en',
      notifications: false,
    },
  },
  {
    id: 'dev-guest',
    name: 'Guest User',
    email: '<EMAIL>',
    role: 'guest',
    avatar: '👻',
    permissions: ['read'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    preferences: {
      theme: 'light',
      language: 'en',
      notifications: false,
    },
  },
];

// Authentication credentials for testing
export const authCredentials: AuthCredential[] = [
  { email: '<EMAIL>', password: 'password', userId: '1' },
  { email: '<EMAIL>', password: 'password', userId: '2' },
  { email: '<EMAIL>', password: 'password', userId: '3' },
  { email: '<EMAIL>', password: 'password', userId: '4' },
  { email: '<EMAIL>', password: 'password', userId: '5' },
  // Dev users
  { email: '<EMAIL>', password: 'dev', userId: 'dev-admin' },
  { email: '<EMAIL>', password: 'dev', userId: 'dev-user' },
  { email: '<EMAIL>', password: 'dev', userId: 'dev-moderator' },
  { email: '<EMAIL>', password: 'dev', userId: 'dev-guest' },
  // Quick login credentials for development
  { email: '<EMAIL>', password: 'admin123', userId: '1' },
  { email: '<EMAIL>', password: 'manager123', userId: '2' },
  { email: '<EMAIL>', password: 'user123', userId: '4' },
  { email: '<EMAIL>', password: 'moderator123', userId: '3' },
  { email: 'demo', password: 'demo', userId: '1' },
  { email: 'test', password: 'test', userId: '2' },
];

// Combine all users for lookup
export const allUsers = [...users, ...devUsers];

// Helper functions
export const getUserById = (id: string): User | undefined => {
  return allUsers.find(user => user.id === id);
};

export const getUserByEmail = (email: string): User | undefined => {
  return allUsers.find(user => user.email === email);
};

export const getCredentialByEmail = (
  email: string
): AuthCredential | undefined => {
  return authCredentials.find(cred => cred.email === email);
};

export const validateCredentials = (
  email: string,
  password: string
): User | null => {
  const credential = getCredentialByEmail(email);
  if (credential && credential.password === password) {
    return getUserById(credential.userId) || null;
  }
  return null;
};

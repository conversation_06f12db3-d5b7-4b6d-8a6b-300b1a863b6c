import React, { useMemo } from 'react';
import type { AppData, NavLink } from '../utils';

export interface AppHeaderData {
  name: string;
  icon: React.ReactNode;
  navLinks: NavLink[];
}

export const useAppHeaderData = (
  appData: AppData | null,
  updatedNavLinks: NavLink[]
): AppHeaderData => {
  return useMemo(() => {
    if (!appData) {
      return {
        name: 'Not Found',
        icon: React.createElement(
          'div',
          {
            className: 'w-8 h-8 rounded-lg flex items-center justify-center text-white text-lg bg-gray-500'
          },
          '?'
        ),
        navLinks: [],
      };
    }

    return {
      name: appData.title,
      icon: React.createElement(
        'div',
        {
          className: 'w-8 h-8 rounded-lg flex items-center justify-center text-white text-lg',
          style: { backgroundColor: appData.color }
        },
        appData.icon
      ),
      navLinks: updatedNavLinks,
    };
  }, [appData?.title, appData?.icon, appData?.color, updatedNavLinks]);
};

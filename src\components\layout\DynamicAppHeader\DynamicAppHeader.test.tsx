import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import DynamicAppHeader from './DynamicAppHeader';
import type { DynamicAppHeaderProps } from './DynamicAppHeader';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      surface: '#ffffff',
      border: '#e5e7eb',
      text: '#111827',
      mutedForeground: '#6b7280',
      primary: '#3b82f6',
      primaryForeground: '#ffffff',
      background: '#f9fafb',
      hover: '#f3f4f6',
      muted: '#f9fafb',
      error: '#ef4444',
      errorForeground: '#ffffff',
    },
  }),
}));

// Sample test data
const mockAppData = {
  name: 'Test App',
  icon: <div data-testid="app-icon">Icon</div>,
  navLinks: [
    { label: 'Home', href: '/home', isActive: true },
    { label: 'Products', href: '/products', isActive: false },
    { label: 'Orders', href: '/orders', isActive: false },
  ],
};

const mockUserData = {
  name: 'John Doe',
  avatar: <div data-testid="user-avatar">Avatar</div>,
  notifications: [
    { count: 3, icon: <div data-testid="notification-icon">Bell</div> },
  ],
};

const defaultProps: DynamicAppHeaderProps = {
  app: mockAppData,
  user: mockUserData,
};

// Helper function to render component with Router context
const renderWithRouter = (component: React.ReactElement) => {
  return render(<MemoryRouter>{component}</MemoryRouter>);
};

describe('DynamicAppHeader', () => {
  it('renders the app name and icon', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('Test App')).toBeInTheDocument();
    expect(screen.getByTestId('app-icon')).toBeInTheDocument();
  });

  it('renders navigation links with active state', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('renders user information', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByTestId('user-avatar')).toBeInTheDocument();
  });

  it('renders notification badge with count', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByTestId('notification-icon')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('does not render bottom bar elements (now handled by AppDynamicContent)', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    // These elements should not be present in the header anymore
    expect(screen.queryByPlaceholderText('Search...')).not.toBeInTheDocument();
    expect(screen.queryByText('New')).not.toBeInTheDocument();
    expect(screen.queryByText('Upload')).not.toBeInTheDocument();
  });




  it('toggles mobile menu when hamburger button is clicked', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    const menuButton = screen.getByLabelText('Toggle mobile menu');
    fireEvent.click(menuButton);

    // Check if mobile menu items are visible
    const mobileNavLinks = screen.getAllByText('Home');
    expect(mobileNavLinks.length).toBeGreaterThan(1); // Desktop + mobile versions
  });

  it('handles notification count display correctly', () => {
    const propsWithHighCount = {
      ...defaultProps,
      user: {
        ...mockUserData,
        notifications: [
          { count: 150, icon: <div data-testid="notification-icon">Bell</div> },
        ],
      },
    };

    renderWithRouter(<DynamicAppHeader {...propsWithHighCount} />);
    expect(screen.getByText('99+')).toBeInTheDocument();
  });

  it('renders with custom test id', () => {
    renderWithRouter(
      <DynamicAppHeader {...defaultProps} data-testid="custom-header" />
    );
    expect(screen.getByTestId('custom-header')).toBeInTheDocument();
  });
});

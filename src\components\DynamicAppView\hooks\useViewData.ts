import { useMemo } from 'react';

export interface ViewData {
  title: string;
  actions: Array<{
    label: string;
    onClick: () => void;
    isPrimary: boolean;
  }>;
  search: {
    filterTags: Array<{ id: string; label: string; removable: boolean }>;
    filterItems: Array<{
      id: string;
      label: string;
      selected: boolean;
      hasDropdown?: boolean;
    }>;
    groupByItems: Array<{
      id: string;
      label: string;
      hasDropdown?: boolean;
    }>;
    favoriteItems: Array<{
      id: string;
      label: string;
      selected: boolean;
    }>;
    onSearch: (query: string) => void;
    onTagRemove: (tagId: string) => void;
    onFilterSelect: (filterId: string) => void;
    onGroupBySelect: (groupId: string) => void;
    onFavoriteSelect: (favoriteId: string) => void;
    onFavoriteDelete: (favoriteId: string) => void;
    onAddCustomFilter: () => void;
    onAddCustomGroup: () => void;
    onSaveCurrentSearch: () => void;
    filters: Array<{ id: string; label: string }>;
    onRemoveFilter: (id: any) => void;
  };
  pagination: {
    currentRange: string;
    onNext: () => void;
    onPrev: () => void;
  };
  viewModes: Array<{ name: string; icon: string }>;
  activeViewMode: string;
}

export const useViewData = (currentViewTitle: string): ViewData => {
  return useMemo(() => ({
    title: currentViewTitle,
    actions: [
      {
        label: 'New',
        onClick: () => console.log('New clicked'),
        isPrimary: true,
      },
      {
        label: 'Import',
        onClick: () => console.log('Import clicked'),
        isPrimary: false,
      },
    ],
    search: {
      filterTags: [{ id: 'active', label: 'Active Records', removable: true }],
      filterItems: [
        { id: 'active', label: 'Active', selected: true },
        { id: 'archived', label: 'Archived', selected: false },
        { id: 'draft', label: 'Draft', selected: false },
        { id: 'published', label: 'Published', selected: false },
        { id: 'date-range', label: 'Date Range', hasDropdown: true },
      ],
      groupByItems: [
        { id: 'status', label: 'Status' },
        { id: 'category', label: 'Category' },
        { id: 'created-date', label: 'Created Date', hasDropdown: true },
        { id: 'assigned-user', label: 'Assigned User' },
      ],
      favoriteItems: [
        { id: 'my-records', label: 'My Records', selected: false },
        { id: 'recent-activity', label: 'Recent Activity', selected: false },
        { id: 'high-priority', label: 'High Priority', selected: true },
        { id: 'pending-review', label: 'Pending Review', selected: false },
      ],
      onSearch: (query: string) => console.log('Search:', query),
      onTagRemove: (tagId: string) => console.log('Remove tag:', tagId),
      onFilterSelect: (filterId: string) =>
        console.log('Filter selected:', filterId),
      onGroupBySelect: (groupId: string) =>
        console.log('Group by selected:', groupId),
      onFavoriteSelect: (favoriteId: string) =>
        console.log('Favorite selected:', favoriteId),
      onFavoriteDelete: (favoriteId: string) =>
        console.log('Favorite deleted:', favoriteId),
      onAddCustomFilter: () => console.log('Add custom filter'),
      onAddCustomGroup: () => console.log('Add custom group'),
      onSaveCurrentSearch: () => console.log('Save current search'),
      filters: [
        { id: 'active', label: 'Active' },
        { id: 'archived', label: 'Archived' },
      ],
      onRemoveFilter: (id: any) => console.log('Remove filter:', id),
    },
    pagination: {
      currentRange: '1-20 / 100',
      onNext: () => console.log('Next page'),
      onPrev: () => console.log('Previous page'),
    },
    viewModes: [
      { name: 'List', icon: '📋' },
      { name: 'Grid', icon: '⊞' },
      { name: 'Chart', icon: '📊' },
    ],
    activeViewMode: 'List',
  }), [currentViewTitle]);
};

// Base Dropdown Types
// Shared types for all dropdown components

export interface BaseDropdownItem {
  id: string;
  label: string;
  disabled?: boolean;
  hidden?: boolean;
}

export interface DropdownItem extends BaseDropdownItem {
  icon?: React.ReactNode;
  onClick?: () => void;
  isDivider?: boolean;
  shortcut?: string;
  description?: string;
  children?: DropdownItem[];
}

export interface DropdownSection {
  id: string;
  title?: string;
  items: DropdownItem[];
  icon?: React.ReactNode;
  collapsible?: boolean;
  collapsed?: boolean;
}

export type DropdownAlign = 'left' | 'right' | 'center';
export type DropdownSize = 'small' | 'medium' | 'large';
export type DropdownVariant = 'default' | 'menu' | 'filter' | 'select';

export interface BaseDropdownProps {
  // Core functionality
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  
  // Positioning
  align?: DropdownAlign;
  offset?: number;
  
  // Appearance
  size?: DropdownSize;
  variant?: DropdownVariant;
  width?: string | number;
  maxHeight?: string | number;
  
  // Behavior
  closeOnSelect?: boolean;
  closeOnClickOutside?: boolean;
  closeOnEscape?: boolean;
  
  // Styling
  className?: string;
  contentClassName?: string;
  
  // Accessibility
  'aria-label'?: string;
  'data-testid'?: string;
}

export interface DropdownTriggerProps {
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
  asChild?: boolean;
}

export interface DropdownContentProps {
  children: React.ReactNode;
  className?: string;
  sideOffset?: number;
  alignOffset?: number;
}

export interface DropdownItemProps extends Omit<DropdownItem, 'id'> {
  children?: React.ReactNode;
  className?: string;
  onSelect?: () => void;
}

export interface DropdownSectionProps {
  title?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export interface DropdownSeparatorProps {
  className?: string;
}

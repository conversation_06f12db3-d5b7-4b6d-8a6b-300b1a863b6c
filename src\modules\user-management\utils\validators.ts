// User Management Validators
import type { UserFormData, UserFormErrors, UserRole } from '../types';

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate user name
 */
export const validateName = (name: string): boolean => {
  return name.trim().length >= 2 && name.trim().length <= 50;
};

/**
 * Validate user role
 */
export const validateRole = (role: string): role is UserRole => {
  const validRoles: UserRole[] = ['user', 'admin', 'moderator', 'viewer'];
  return validRoles.includes(role as UserRole);
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): boolean => {
  // At least 8 characters, one uppercase, one lowercase, one number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

/**
 * Comprehensive user form validation
 */
export const validateUserForm = (formData: UserFormData): UserFormErrors => {
  const errors: UserFormErrors = {};

  // Validate name
  if (!formData.name.trim()) {
    errors.name = 'Name is required';
  } else if (!validateName(formData.name)) {
    errors.name = 'Name must be between 2 and 50 characters';
  }

  // Validate email
  if (!formData.email.trim()) {
    errors.email = 'Email is required';
  } else if (!validateEmail(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Validate role
  if (!formData.role) {
    errors.role = 'Role is required';
  } else if (!validateRole(formData.role)) {
    errors.role = 'Please select a valid role';
  }

  return errors;
};

/**
 * Check if form has any validation errors
 */
export const hasValidationErrors = (errors: UserFormErrors): boolean => {
  return Object.keys(errors).length > 0;
};

/**
 * Validate bulk user data
 */
export const validateBulkUsers = (users: UserFormData[]): { valid: UserFormData[]; invalid: { user: UserFormData; errors: UserFormErrors }[] } => {
  const valid: UserFormData[] = [];
  const invalid: { user: UserFormData; errors: UserFormErrors }[] = [];

  users.forEach(user => {
    const errors = validateUserForm(user);
    if (hasValidationErrors(errors)) {
      invalid.push({ user, errors });
    } else {
      valid.push(user);
    }
  });

  return { valid, invalid };
};

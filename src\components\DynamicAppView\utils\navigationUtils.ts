import React from 'react';

export interface NavLink {
  label: string;
  href: string;
  isActive?: boolean;
}

export interface AppData {
  title: string;
  icon: React.ReactNode;
  color: string;
  navLinks?: NavLink[];
  views?: Record<string, { title: string; content: string }>;
}

/**
 * Updates navigation links to show active state based on current view
 */
export const updateNavLinksActiveState = (
  appData: AppData,
  menuId: string,
  viewId: string
): NavLink[] => {
  return appData.navLinks
    ? appData.navLinks.map(link => ({
        ...link,
        isActive: link.href.includes(`view=${viewId}`),
      }))
    : [
        {
          label: 'Dashboard',
          href: `/app?menu=${menuId}&view=dashboard`,
          isActive: viewId === 'dashboard',
        },
      ];
};

/**
 * Gets the current view data based on viewId
 */
export const getCurrentView = (
  appData: AppData,
  viewId: string
): { title: string; content: string } => {
  return appData.views
    ? appData.views[viewId as keyof typeof appData.views] ||
      appData.views.dashboard
    : {
        title: `${appData.title} Dashboard`,
        content: `Welcome to ${appData.title}. This is the main dashboard view.`,
      };
};

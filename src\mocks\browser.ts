import { setupWorker } from 'msw/browser';
import { handlers } from './handlers';
import { initializeScenarios } from './scenarios';
import { getCurrentConfig } from './config';

// Setup MSW worker for browser environment
export const worker = setupWorker(...handlers);

// Enhanced worker start function with configuration
export const startWorker = async () => {
  const config = getCurrentConfig();

  if (!config.enabled) {
    console.log('MSW is disabled by configuration');
    return;
  }

  try {
    await worker.start({
      onUnhandledRequest: config.enableLogging ? 'warn' : 'bypass',
      quiet: !config.enableLogging,
    });

    // Initialize scenarios after worker starts
    initializeScenarios();

    if (config.enableLogging) {
      console.log(
        'MSW worker started successfully with configuration:',
        config
      );
    }
  } catch (error) {
    console.error('Failed to start MSW worker:', error);
    throw error;
  }
};

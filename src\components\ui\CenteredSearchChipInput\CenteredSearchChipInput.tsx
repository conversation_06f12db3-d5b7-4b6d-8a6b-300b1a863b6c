import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import {
  StarIcon,
  StarFilledIcon,
  FilterIcon,
  GroupIcon,
  XIcon,
  ChevronDownIcon,
} from '../../icons';
import { FilterDropdown } from '../../global/Dropdown';

// Custom StarIcon component to handle filled state
const CustomStarIcon = ({ filled = false }: { filled?: boolean }) => {
  return filled ? (
    <StarFilledIcon className="w-4 h-4" />
  ) : (
    <StarIcon className="w-4 h-4" />
  );
};

export interface FilterTag {
  id: string;
  label: string;
  removable?: boolean;
  type?: 'filter' | 'groupBy' | 'favorite';
}

export interface FilterItem {
  id: string;
  label: string;
  selected?: boolean;
  hasDropdown?: boolean;
  onClick?: () => void;
}

export interface FavoriteItem {
  id: string;
  label: string;
  selected?: boolean;
  onDelete?: () => void;
  onClick?: () => void;
}

export interface GroupByItem {
  id: string;
  label: string;
  hasDropdown?: boolean;
  onClick?: () => void;
}

// Legacy type aliases for backward compatibility
export type ChipData = FilterTag;
export type FilterOption = FilterItem;

export interface CenteredSearchChipInputProps {
  placeholder?: string;
  filterTags?: FilterTag[];
  filterItems?: FilterItem[];
  groupByItems?: GroupByItem[];
  favoriteItems?: FavoriteItem[];
  onSearch?: (query: string) => void;
  onTagRemove?: (tagId: string) => void;
  onFilterSelect?: (filterId: string) => void;
  onGroupBySelect?: (groupId: string) => void;
  onFavoriteSelect?: (favoriteId: string) => void;
  onFavoriteDelete?: (favoriteId: string) => void;
  onAddCustomFilter?: () => void;
  onAddCustomGroup?: () => void;
  onSaveCurrentSearch?: () => void;
  className?: string;
  'data-testid'?: string;
}

const CenteredSearchChipInput: React.FC<CenteredSearchChipInputProps> = ({
  placeholder = 'Search...',
  filterTags = [],
  filterItems = [],
  groupByItems = [],
  favoriteItems = [],
  onSearch,
  onTagRemove,
  onFilterSelect,
  onGroupBySelect,
  onFavoriteSelect,
  onFavoriteDelete,
  onAddCustomFilter,
  onAddCustomGroup,
  onSaveCurrentSearch,
  className = '',
  'data-testid': testId,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { colors } = useThemeStore();
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearch?.(value);
  };

  const handleChevronClick = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  const handleTagRemove = (tagId: string) => {
    onTagRemove?.(tagId);
  };

  // Get chip styling and icon based on tag type
  const getChipStyle = (type?: 'filter' | 'groupBy' | 'favorite') => {
    switch (type) {
      case 'filter':
        return {
          backgroundColor: odooColors.filtersIcon + '20', // 20% opacity
          borderColor: odooColors.filtersIcon,
          color: odooColors.filtersIcon,
          icon: <FilterIcon className="w-3 h-3" />
        };
      case 'groupBy':
        return {
          backgroundColor: odooColors.groupByIcon + '20', // 20% opacity
          borderColor: odooColors.groupByIcon,
          color: odooColors.groupByIcon,
          icon: <GroupIcon className="w-3 h-3" />
        };
      case 'favorite':
        return {
          backgroundColor: odooColors.favoritesIcon + '20', // 20% opacity
          borderColor: odooColors.favoritesIcon,
          color: odooColors.favoritesIcon,
          icon: <CustomStarIcon filled />
        };
      default:
        return {
          backgroundColor: odooColors.tagBg,
          borderColor: odooColors.borderColor,
          color: odooColors.primaryText,
          icon: null
        };
    }
  };

  // Odoo-style color scheme based on app theme
  const odooColors = {
    // Primary background color: #2C2C39 equivalent in app theme
    primaryBg: colors.surface, // #1f2937 in dark theme
    // Primary text color: #FFFFFF
    primaryText: colors.text, // #f9fafb in dark theme
    // Secondary text color: #B0B0B0
    secondaryText: colors.textMuted, // #9ca3af in dark theme
    // Accent Colors
    filtersIcon: '#E91E63', // pink
    groupByIcon: '#00BCD4', // cyan
    favoritesIcon: '#FFC107', // yellow
    // Hover background color: #3C3C4F equivalent
    hoverBg: colors.hover, // #374151 in dark theme
    // Border color / Dividers: #41415B equivalent
    borderColor: colors.border, // #4b5563 in dark theme
    // Tag background color: #4E4E68 equivalent
    tagBg: colors.surfaceTertiary, // #4b5563 in dark theme
    // Trash icon hover color
    trashHover: colors.error, // #ef4444 in dark theme
  };

  // Filter items based on search query
  const getFilteredItems = (items: FilterItem[]) => {
    if (!searchQuery) return items;
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getFilteredGroupByItems = (items: GroupByItem[]) => {
    if (!searchQuery) return items;
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getFilteredFavoriteItems = (items: FavoriteItem[]) => {
    if (!searchQuery) return items;
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  return (
    <div
      className={cn('relative w-full max-w-4xl mx-auto', className)}
      data-testid={testId}
      ref={dropdownRef}
    >
      <form onSubmit={handleSearchSubmit} className="relative">
        {/* Search Bar with Filter Tags - Compact Design */}
        <div
          className="flex items-center h-8 px-2 py-1 pr-8 border rounded transition-all duration-200 focus-within:ring-2 focus-within:ring-offset-1 relative"
          style={{
            backgroundColor: odooColors.primaryBg,
            borderColor: odooColors.borderColor,
            color: odooColors.primaryText,
          }}
        >
          {/* Filter Tags and Search Input */}
          <div className="flex items-center gap-1 flex-wrap flex-1">
            {filterTags.map(tag => {
              const chipStyle = getChipStyle(tag.type);
              return (
                <div
                  key={tag.id}
                  className="inline-flex items-center gap-0 rounded text-xs font-medium transition-colors border overflow-hidden"
                  style={{
                    backgroundColor: chipStyle.backgroundColor,
                    borderColor: chipStyle.borderColor,
                    color: chipStyle.color,
                  }}
                >
                  {chipStyle.icon && (
                    <span
                      className="flex items-center justify-center px-1.5 py-0.5"
                      style={{
                        backgroundColor: chipStyle.color,
                        color: '#ffffff'
                      }}
                    >
                      {chipStyle.icon}
                    </span>
                  )}
                  <span className="truncate max-w-[100px] px-1.5 py-0.5">{tag.label}</span>
                  {tag.removable && (
                    <button
                      type="button"
                      onClick={() => handleTagRemove(tag.id)}
                      className="flex items-center justify-center px-1 py-0.5 hover:opacity-70 transition-opacity"
                      style={{
                        backgroundColor: 'transparent',
                        color: chipStyle.color
                      }}
                      aria-label={`Remove ${tag.label}`}
                    >
                      <XIcon className="w-3 h-3" />
                    </button>
                  )}
                </div>
              );
            })}

            {/* Search Input */}
            <input
              ref={inputRef}
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder={placeholder}
              className="flex-1 min-w-[100px] bg-transparent border-none outline-none text-sm placeholder-opacity-60"
              style={{
                color: odooColors.primaryText,
              }}
            />
          </div>

          {/* Chevron Down Button - Positioned as suffix */}
          <button
            type="button"
            onClick={handleChevronClick}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 flex items-center justify-center p-1 hover:opacity-70 transition-opacity rounded"
            style={{ color: odooColors.secondaryText }}
            aria-label="Toggle search dropdown"
          >
            <ChevronDownIcon className={cn(
              "w-3 h-3 transition-transform duration-200",
              isDropdownOpen && "rotate-180"
            )} />
          </button>
        </div>

        {/* Filter Dropdown */}
        <FilterDropdown
          trigger={<div />} // Empty trigger since we handle the trigger separately
          isOpen={isDropdownOpen}
          onOpenChange={setIsDropdownOpen}
          filterItems={filterItems}
          groupByItems={groupByItems}
          favoriteItems={favoriteItems}
          searchQuery={searchQuery}
          onFilterSelect={onFilterSelect}
          onGroupBySelect={onGroupBySelect}
          onFavoriteSelect={onFavoriteSelect}
          onFavoriteDelete={onFavoriteDelete}
          onAddCustomFilter={onAddCustomFilter}
          onAddCustomGroup={onAddCustomGroup}
          onSaveCurrentSearch={onSaveCurrentSearch}
          compact={true}
        />
      </form>
    </div>
  );
};

export default CenteredSearchChipInput;

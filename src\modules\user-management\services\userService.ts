// User Service - API operations for user management
import type { User, CreateUserRequest, UpdateUserRequest, UserFilters } from '../types';

export interface UserServiceConfig {
  baseUrl?: string;
  timeout?: number;
}

class UserService {
  private baseUrl: string;
  private timeout: number;

  constructor(config: UserServiceConfig = {}) {
    this.baseUrl = config.baseUrl || '/api';
    this.timeout = config.timeout || 5000;
  }

  /**
   * Fetch all users with optional filtering
   */
  async getUsers(filters?: UserFilters): Promise<User[]> {
    const params = new URLSearchParams();
    
    if (filters?.search) {
      params.append('search', filters.search);
    }
    
    if (filters?.role?.length) {
      params.append('role', filters.role.join(','));
    }
    
    if (filters?.status?.length) {
      params.append('status', filters.status.join(','));
    }

    const url = `${this.baseUrl}/users${params.toString() ? `?${params.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch users: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Get a single user by ID
   */
  async getUserById(id: string): Promise<User> {
    const response = await fetch(`${this.baseUrl}/users/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserRequest): Promise<User> {
    const response = await fetch(`${this.baseUrl}/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error(`Failed to create user: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Update an existing user
   */
  async updateUser(userData: UpdateUserRequest): Promise<User> {
    const response = await fetch(`${this.baseUrl}/users/${userData.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error(`Failed to update user: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Delete a user
   */
  async deleteUser(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/users/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete user: ${response.statusText}`);
    }
  }

  /**
   * Bulk delete users
   */
  async deleteUsers(ids: string[]): Promise<void> {
    const response = await fetch(`${this.baseUrl}/users/bulk-delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids }),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete users: ${response.statusText}`);
    }
  }
}

// Export singleton instance
export const userService = new UserService();

import type { Meta, StoryObj } from '@storybook/react-vite';
import { LoginForm } from './LoginForm';

const meta: Meta<typeof LoginForm> = {
  title: 'Auth/LoginForm',
  component: LoginForm,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A comprehensive login form with email/password authentication, validation, and accessibility features.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onSubmit: {
      action: 'submit',
      description:
        'Callback function called when form is submitted with valid data',
    },
    onForgotPassword: {
      action: 'forgot-password',
      description:
        'Callback function called when forgot password link is clicked',
    },
    onSwitchToOTP: {
      action: 'switch-to-otp',
      description: 'Callback function called when OTP login link is clicked',
    },
    loading: {
      control: { type: 'boolean' },
      description: 'Loading state for the form',
    },
    error: {
      control: { type: 'text' },
      description: 'Error message to display',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    loading: false,
    error: '',
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    error: '',
  },
};

export const WithError: Story = {
  args: {
    loading: false,
    error: 'Invalid email or password. Please try again.',
  },
};

export const WithNetworkError: Story = {
  args: {
    loading: false,
    error: 'Network error. Please check your connection and try again.',
  },
};

export const WithAccountLockedError: Story = {
  args: {
    loading: false,
    error:
      'Account temporarily locked due to multiple failed login attempts. Please try again in 15 minutes or reset your password.',
  },
};

// Interactive story showing form validation
export const ValidationDemo: Story = {
  render: () => {
    return (
      <div className="w-96 space-y-4">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Try the validation:
          </h3>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Submit empty form to see required field errors</li>
            <li>• Enter invalid email format</li>
            <li>• Enter password less than 6 characters</li>
            <li>• Toggle password visibility</li>
            <li>• Use forgot password and OTP links</li>
          </ul>
        </div>
        <LoginForm />
      </div>
    );
  },
};

// Story showing accessibility features
export const AccessibilityDemo: Story = {
  render: () => {
    return (
      <div className="w-96 space-y-4">
        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
            Accessibility Features:
          </h3>
          <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
            <li>• Proper form labels and ARIA attributes</li>
            <li>• Keyboard navigation support</li>
            <li>• Screen reader friendly error messages</li>
            <li>• Focus management and visual indicators</li>
            <li>• High contrast support</li>
          </ul>
        </div>
        <LoginForm />
      </div>
    );
  },
};

// Story with pre-filled data for testing
export const PrefilledForm: Story = {
  render: () => {
    return (
      <div className="w-96">
        <LoginForm />
        <script>
          {`
            // This would be handled by the parent component in real usage
            setTimeout(() => {
              const emailInput = document.querySelector('[data-testid="login-email"]');
              const passwordInput = document.querySelector('[data-testid="login-password"]');
              if (emailInput) emailInput.value = '<EMAIL>';
              if (passwordInput) passwordInput.value = 'password123';
            }, 100);
          `}
        </script>
      </div>
    );
  },
};

// Story showing different states
export const AllStates: Story = {
  render: () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl">
        <div>
          <h3 className="text-lg font-semibold mb-4">Default State</h3>
          <LoginForm />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Loading State</h3>
          <LoginForm loading={true} />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Error State</h3>
          <LoginForm error="Invalid credentials" />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Network Error</h3>
          <LoginForm error="Network connection failed. Please try again." />
        </div>
      </div>
    );
  },
};

import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface CaptionProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  variant?: 'default' | 'helper' | 'error' | 'success' | 'warning' | 'info';
  size?: 'xs' | 'sm';
  weight?: 'normal' | 'medium';
  align?: 'left' | 'center' | 'right';
  truncate?: boolean;
  lineClamp?: 1 | 2 | 3;
  as?: 'p' | 'span' | 'div' | 'small';
  className?: string;
  'data-testid'?: string;
}

const Caption: React.FC<CaptionProps> = ({
  children,
  variant = 'default',
  size = 'xs',
  weight = 'normal',
  align = 'left',
  truncate = false,
  lineClamp,
  as: Component = 'p',
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  // Size classes
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
  };

  // Weight classes
  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
  };

  // Alignment classes
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  // Color mapping based on variant
  const getTextColor = () => {
    switch (variant) {
      case 'error':
        return colors.error;
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'info':
        return colors.info;
      case 'helper':
        return colors.mutedForeground;
      case 'default':
      default:
        return colors.textSecondary;
    }
  };

  // Build classes
  const classes = cn(
    'leading-relaxed',
    sizeClasses[size],
    weightClasses[weight],
    alignClasses[align],
    truncate && 'truncate',
    lineClamp && `line-clamp-${lineClamp}`,
    className
  );

  const style = {
    color: getTextColor(),
    ...props.style,
  };

  return (
    <Component
      className={classes}
      style={style}
      data-testid={testId}
      {...props}
    >
      {children}
    </Component>
  );
};

export default Caption;

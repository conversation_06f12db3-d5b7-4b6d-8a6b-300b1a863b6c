// User Management Formatters
import type { User, UserRole, UserStatus } from '../types';

/**
 * Format user data for display
 */
export const formatUserForDisplay = (user: User) => {
  return {
    ...user,
    displayName: user.name || user.email.split('@')[0],
    initials: user.name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .slice(0, 2)
      .join(''),
    roleDisplay: getRoleDisplayName(user.role),
    statusDisplay: getStatusDisplayName(user.status || 'active'),
    lastActivityDisplay: formatLastActivity(user.lastLoginAt),
    createdAtDisplay: formatDate(user.createdAt),
  };
};

/**
 * Format date for display
 */
export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Format datetime for display
 */
export const formatDateTime = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Format last activity for display
 */
export const formatLastActivity = (lastLoginAt?: string): string => {
  if (!lastLoginAt) return 'Never';
  
  const lastLogin = new Date(lastLoginAt);
  const now = new Date();
  const diffMs = now.getTime() - lastLogin.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMinutes < 1) return 'Just now';
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return formatDate(lastLoginAt);
};

/**
 * Get role display name
 */
export const getRoleDisplayName = (role: UserRole): string => {
  const roleNames: Record<UserRole, string> = {
    admin: 'Administrator',
    moderator: 'Moderator',
    user: 'User',
    viewer: 'Viewer',
  };
  return roleNames[role] || role;
};

/**
 * Get status display name
 */
export const getStatusDisplayName = (status: UserStatus): string => {
  const statusNames: Record<UserStatus, string> = {
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    suspended: 'Suspended',
  };
  return statusNames[status] || status;
};

/**
 * Format user count for display
 */
export const formatUserCount = (count: number): string => {
  if (count === 0) return 'No users';
  if (count === 1) return '1 user';
  return `${count.toLocaleString()} users`;
};

/**
 * Format user list for export
 */
export const formatUsersForExport = (users: User[]) => {
  return users.map(user => ({
    Name: user.name,
    Email: user.email,
    Role: getRoleDisplayName(user.role),
    Status: getStatusDisplayName(user.status || 'active'),
    'Created At': formatDate(user.createdAt),
    'Last Login': formatLastActivity(user.lastLoginAt),
  }));
};

/**
 * Format user permissions for display
 */
export const formatUserPermissions = (user: User): string[] => {
  if (!user.permissions) return [];
  return user.permissions.map(permission => permission.name);
};

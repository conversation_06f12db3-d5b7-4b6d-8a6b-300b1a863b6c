# User Management Module

Complete user administration and management functionality for Nexed Web.

## 🏗️ Module Structure

```
src/modules/user-management/
├── UserManagementModule.tsx  # Main module component
├── index.ts                  # Module exports
├── README.md                 # This file
├── components/               # React components
│   ├── UserForm/            # User creation/editing form
│   ├── UserCard/            # User display card
│   ├── UserList/            # User list with filtering
│   ├── AddUserModal/        # Modal for adding users
│   └── index.ts             # Component exports
├── services/                # API services
│   ├── userService.ts       # User CRUD operations
│   ├── roleService.ts       # Role management
│   ├── permissionService.ts # Permission management
│   ├── types.ts             # Service types
│   └── index.ts             # Service exports
├── hooks/                   # Custom React hooks
│   ├── useUsers.ts          # User data management
│   ├── useUserForm.ts       # Form state management
│   ├── useUserFilters.ts    # Filter state management
│   ├── useUserActions.ts    # User CRUD actions
│   ├── types.ts             # Hook return types
│   └── index.ts             # Hook exports
├── types/                   # TypeScript definitions
│   └── index.ts             # Type exports
├── utils/                   # Utility functions
│   ├── validators.ts        # Form validation
│   ├── helpers.ts           # Helper functions
│   ├── formatters.ts        # Data formatting
│   └── index.ts             # Utility exports
└── __tests__/               # Test files
    └── UserManagementModule.test.tsx
```

## 🚀 Features

### ✅ Core Functionality
- **User CRUD Operations**: Create, read, update, delete users
- **Role Management**: Assign and manage user roles (admin, moderator, user, viewer)
- **Permission System**: Granular permission control
- **User Profiles**: Comprehensive user information management
- **Optimistic Updates**: Immediate UI feedback with rollback on errors

### ✅ User Interface
- **User List**: Paginated list with search and filtering
- **User Cards**: Clean user display with actions
- **Add User Modal**: Modal form for creating new users
- **Form Validation**: Comprehensive client-side validation
- **Responsive Design**: Works on all screen sizes

### ✅ Data Management
- **SWR Integration**: Efficient data fetching and caching
- **Real-time Updates**: Automatic data synchronization
- **Error Handling**: Graceful error states and recovery
- **Loading States**: Proper loading indicators

## 🎯 Usage

### Basic Usage

```typescript
import { UserManagementModule } from '../modules/user-management';

function App() {
  return <UserManagementModule />;
}
```

### Using Individual Components

```typescript
import { UserList, AddUserModal } from '../modules/user-management';

function UserManagement() {
  const handleUserAdded = () => {
    // Refresh user list
  };

  return (
    <div>
      <AddUserModal onUserAdded={handleUserAdded} />
      <UserList 
        onUserEdit={handleEdit}
        onUserDelete={handleDelete}
      />
    </div>
  );
}
```

### Using Hooks

```typescript
import { useUsers, useUserActions } from '../modules/user-management';

function CustomUserComponent() {
  const { users, loading, error } = useUsers();
  const { createUser, updateUser, deleteUser } = useUserActions();

  // Component logic
}
```

## 🔧 Configuration

### Service Configuration

```typescript
import { userService } from '../modules/user-management';

// Configure base URL
userService.configure({
  baseUrl: '/api/v1',
  timeout: 10000,
});
```

## 🧪 Testing

The module includes comprehensive tests:

```bash
# Run user management tests
npm test src/modules/user-management

# Run specific test file
npm test UserManagementModule.test.tsx
```

## 📋 API Integration

### Required Endpoints

The module expects the following API endpoints:

- `GET /api/users` - Fetch users list
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `GET /api/roles` - Fetch roles
- `GET /api/permissions` - Fetch permissions

### Mock Data

Development uses MSW (Mock Service Worker) for API mocking:

```typescript
// Mock handlers are automatically configured
// See: src/mocks/handlers/users.ts
```

## 🎨 Styling

The module uses:
- **Tailwind CSS** for styling
- **Theme Store** for consistent colors
- **Responsive Design** principles
- **Dark/Light Mode** support

## 🔄 State Management

- **SWR** for server state
- **React Hooks** for local state
- **Optimistic Updates** for better UX
- **Error Boundaries** for error handling

## 📚 Type Safety

Full TypeScript support with:
- Interface definitions for all data structures
- Type-safe API calls
- Validated form inputs
- Comprehensive error types

## 🚀 Performance

- **Code Splitting** ready
- **Lazy Loading** components
- **Memoized** expensive operations
- **Optimized** re-renders

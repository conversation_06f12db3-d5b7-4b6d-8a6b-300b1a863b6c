// User Management Module Types
// Centralized type definitions for user management functionality

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  createdAt?: string;
  updatedAt?: string;
  avatar?: string;
  status?: UserStatus;
  lastLoginAt?: string;
  permissions?: Permission[];
}

export type UserRole = 'user' | 'admin' | 'moderator' | 'viewer';

export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  role: UserRole;
  sendInvitation?: boolean;
}

export interface UpdateUserRequest {
  id: string;
  name?: string;
  email?: string;
  role?: UserRole;
  status?: UserStatus;
  permissions?: string[];
}

export interface UserFilters {
  role?: UserRole[];
  status?: UserStatus[];
  search?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface UserListState {
  users: User[];
  loading: boolean;
  error: string | null;
  filters: UserFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UserFormData {
  name: string;
  email: string;
  role: UserRole;
}

export interface UserFormErrors {
  name?: string;
  email?: string;
  role?: string;
  general?: string;
}

// API Response types
export interface UserApiResponse {
  data: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CreateUserResponse {
  data: User;
  message: string;
}

export interface UpdateUserResponse {
  data: User;
  message: string;
}

export interface DeleteUserResponse {
  message: string;
}

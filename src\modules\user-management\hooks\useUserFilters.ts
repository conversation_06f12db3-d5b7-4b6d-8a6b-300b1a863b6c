import { useState, useCallback } from 'react';
import type { UserFilters } from '../types';
import type { UseUserFiltersReturn } from './types';

const initialFilters: UserFilters = {
  role: undefined,
  status: undefined,
  search: undefined,
  dateFrom: undefined,
  dateTo: undefined,
};

/**
 * Hook for managing user list filters
 */
export function useUserFilters(): UseUserFiltersReturn {
  const [filters, setFilters] = useState<UserFilters>(initialFilters);

  const setFilter = useCallback(<K extends keyof UserFilters>(
    key: K,
    value: UserFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, []);

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== null && value !== ''
  );

  return {
    filters,
    setFilter,
    clearFilters,
    hasActiveFilters,
  };
}

// UI Components - Specialized UI components for specific use cases
// Basic components have been moved to better locations:
// - Button, Input, Label, TextArea → components/forms/
// - Card, Text, Heading, Separator → components/global/
// - ViewModeSwitcher, ViewToggle → components/global/ViewModeSelector
// - Dropdown, FilterDropdown → components/global/Dropdown
// - Modal → components/feedback/
// - Pagination → components/data-display/
// - TopNavigation → components/navigation/
// - ThemeToggle, UserAvatarDropdown → components/global/

// Phone Input (specialized input component)
export { default as PhoneInput } from './PhoneInput/PhoneInput';
export type { PhoneInputProps } from './PhoneInput/PhoneInput';

// Dashboard Components
export { default as NotificationBar } from './NotificationBar/NotificationBar';
export type { NotificationBarProps } from './NotificationBar/NotificationBar';

export { default as AppTile } from './AppTile/AppTile';
export type { AppTileProps } from './AppTile/AppTile';

export { default as SearchOverlay } from './SearchOverlay/SearchOverlay';
export type { SearchOverlayProps } from './SearchOverlay/SearchOverlay';

export { default as CustomerSupportModal } from './CustomerSupportModal/CustomerSupportModal';
export type { CustomerSupportModalProps } from './CustomerSupportModal/CustomerSupportModal';


export { default as CenteredSearchChipInput } from './CenteredSearchChipInput/CenteredSearchChipInput';
export type {
  CenteredSearchChipInputProps,
  ChipData,
  FilterOption,
} from './CenteredSearchChipInput/CenteredSearchChipInput';

// Typography Components (remaining specialized ones)
export { default as Caption } from './Caption/Caption';
export type { CaptionProps } from './Caption/Caption';

// Company Selector
export { default as CompanySelector } from './CompanySelector/CompanySelector';
export type { CompanySelectorProps } from './CompanySelector/CompanySelector';
// Modal Components (remaining specialized ones)
export { RelativeModal } from './RelativeModal';
export type { RelativeModalProps } from './RelativeModal';

// TODO: Add more components as they are implemented
// export { default as TextArea } from './TextArea/TextArea'
// export { default as Select } from './Select/Select'
// export { default as Checkbox } from './Checkbox/Checkbox'
// export { default as Radio } from './Radio/Radio'
// export { default as Badge } from './Badge/Badge'
// export { default as Avatar } from './Avatar/Avatar'
// export { default as Divider } from './Divider/Divider'
// export { default as Spinner } from './Spinner/Spinner'
// export { default as Skeleton } from './Skeleton/Skeleton'
// export { default as Icon } from './Icon/Icon'

import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import type { ViewModeSelectorProps, ViewModeOption } from './types';

/**
 * Unified ViewModeSelector component that consolidates all view mode switching patterns
 * Supports toggle, switcher, selector, and compact variants
 */
export function ViewModeSelector<T extends string = string>({
  options,
  value,
  onChange,
  variant = 'switcher',
  size = 'medium',
  orientation = 'horizontal',
  showLabels = true,
  showDescriptions = false,
  showPreview = false,
  allowDeselect = false,
  className = '',
  buttonClassName = '',
  'aria-label': ariaLabel,
  'data-testid': testId,
}: ViewModeSelectorProps<T>) {
  const { colors } = useThemeStore();

  const handleOptionClick = (optionId: T) => {
    if (allowDeselect && value === optionId) {
      // Allow deselecting current option
      onChange('' as T);
    } else if (optionId !== value) {
      onChange(optionId);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: 'p-0.5',
          button: 'p-1 text-xs',
          icon: 'w-3 h-3',
          text: 'text-xs',
        };
      case 'large':
        return {
          container: 'p-2',
          button: 'p-3 text-base',
          icon: 'w-6 h-6',
          text: 'text-base',
        };
      default: // medium
        return {
          container: 'p-1',
          button: 'p-2 text-sm',
          icon: 'w-4 h-4',
          text: 'text-sm',
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // Render different variants
  switch (variant) {
    case 'toggle':
      return (
        <div
          className={cn(
            'inline-flex rounded-lg border',
            sizeClasses.container,
            orientation === 'vertical' ? 'flex-col' : 'flex-row',
            className
          )}
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
          role="radiogroup"
          aria-label={ariaLabel || 'View mode selector'}
          data-testid={testId}
        >
          {options.map((option, index) => {
            const isActive = option.id === value;
            const isFirst = index === 0;
            const isLast = index === options.length - 1;
            
            return (
              <button
                key={option.id}
                onClick={() => handleOptionClick(option.id)}
                disabled={option.disabled}
                className={cn(
                  'flex items-center gap-2 font-medium transition-all duration-200',
                  sizeClasses.button,
                  orientation === 'horizontal' 
                    ? cn(
                        isFirst && 'rounded-l-lg',
                        isLast && 'rounded-r-lg'
                      )
                    : cn(
                        isFirst && 'rounded-t-lg',
                        isLast && 'rounded-b-lg'
                      ),
                  isActive
                    ? 'text-white shadow-sm'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700',
                  option.disabled && 'opacity-50 cursor-not-allowed',
                  buttonClassName
                )}
                style={{
                  backgroundColor: isActive ? colors.primary : 'transparent',
                  color: isActive ? '#ffffff' : colors.text,
                }}
                role="radio"
                aria-checked={isActive}
                aria-label={option.name}
                title={option.description || option.name}
              >
                <span className={sizeClasses.icon}>{option.icon}</span>
                {showLabels && (
                  <span className={sizeClasses.text}>{option.name}</span>
                )}
              </button>
            );
          })}
        </div>
      );

    case 'compact':
      return (
        <div
          className={cn(
            'inline-flex rounded-lg border',
            sizeClasses.container,
            orientation === 'vertical' ? 'flex-col' : 'flex-row',
            className
          )}
          style={{
            backgroundColor: colors.backgroundSecondary,
            borderColor: colors.border,
          }}
          role="radiogroup"
          aria-label={ariaLabel || 'View mode selector'}
          data-testid={testId}
        >
          {options.map((option, index) => {
            const isActive = option.id === value;
            const isFirst = index === 0;
            const isLast = index === options.length - 1;
            
            return (
              <button
                key={option.id}
                onClick={() => handleOptionClick(option.id)}
                disabled={option.disabled}
                className={cn(
                  'transition-colors',
                  sizeClasses.button,
                  orientation === 'horizontal' 
                    ? cn(
                        isFirst && 'rounded-l-lg',
                        isLast && 'rounded-r-lg'
                      )
                    : cn(
                        isFirst && 'rounded-t-lg',
                        isLast && 'rounded-b-lg'
                      ),
                  isActive
                    ? 'text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700',
                  option.disabled && 'opacity-50 cursor-not-allowed',
                  buttonClassName
                )}
                style={{
                  backgroundColor: isActive ? colors.primary : 'transparent',
                  color: isActive ? 'white' : colors.text,
                }}
                role="radio"
                aria-checked={isActive}
                aria-label={option.name}
                title={option.name}
              >
                <span className={sizeClasses.icon}>{option.icon}</span>
              </button>
            );
          })}
        </div>
      );

    case 'selector':
      return (
        <div
          className={cn('space-y-4', className)}
          role="radiogroup"
          aria-label={ariaLabel || 'View mode selector'}
          data-testid={testId}
        >
          {showDescriptions && (
            <div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
                View Mode
              </h3>
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                Choose how content is displayed.
              </p>
            </div>
          )}

          <div className={cn(
            'space-y-3',
            orientation === 'horizontal' && 'flex space-y-0 space-x-3'
          )}>
            {options.map((option) => {
              const isActive = option.id === value;
              
              return (
                <div
                  key={option.id}
                  className={cn(
                    'border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md',
                    isActive
                      ? 'ring-2 ring-opacity-50'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-800',
                    option.disabled && 'opacity-50 cursor-not-allowed',
                    orientation === 'horizontal' && 'flex-1'
                  )}
                  style={{
                    borderColor: isActive ? colors.primary : colors.border,
                    ringColor: isActive ? colors.primary : 'transparent',
                    backgroundColor: isActive ? `${colors.primary}05` : 'transparent',
                  }}
                  onClick={() => !option.disabled && handleOptionClick(option.id)}
                  role="radio"
                  aria-checked={isActive}
                  aria-label={option.name}
                >
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">{option.icon}</div>
                    <div className="flex-1">
                      <h4 className="font-medium" style={{ color: colors.text }}>
                        {option.name}
                      </h4>
                      {option.description && (
                        <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
                          {option.description}
                        </p>
                      )}
                      {showPreview && option.preview && (
                        <p className="text-xs mt-2 italic" style={{ color: colors.textSecondary }}>
                          {option.preview}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );

    default: // switcher
      return (
        <div
          className={cn(
            'flex items-center rounded-lg border',
            sizeClasses.container,
            orientation === 'vertical' ? 'flex-col' : 'flex-row',
            className
          )}
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
          role="radiogroup"
          aria-label={ariaLabel || 'View mode selector'}
          data-testid={testId}
        >
          {options.map((option) => {
            const isActive = option.id === value;
            
            return (
              <button
                key={option.id}
                onClick={() => handleOptionClick(option.id)}
                disabled={option.disabled}
                className={cn(
                  'rounded-md transition-all duration-200 relative',
                  sizeClasses.button,
                  option.disabled && 'opacity-50 cursor-not-allowed',
                  buttonClassName
                )}
                style={{
                  backgroundColor: isActive ? colors.primary : 'transparent',
                  color: isActive ? colors.primaryForeground : colors.mutedForeground,
                  boxShadow: isActive ? `0 1px 3px ${colors.shadow}` : 'none',
                }}
                onMouseEnter={(e) => {
                  if (!isActive && !option.disabled) {
                    e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                    e.currentTarget.style.color = colors.text;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive && !option.disabled) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = colors.mutedForeground;
                  }
                }}
                role="radio"
                aria-checked={isActive}
                aria-label={`Switch to ${option.name} view`}
                title={option.description || `${option.name} view`}
              >
                <span className={sizeClasses.icon}>{option.icon}</span>
                {showLabels && (
                  <span className={cn('ml-2', sizeClasses.text)}>{option.name}</span>
                )}
                
                {/* Active indicator */}
                {isActive && (
                  <div
                    className="absolute inset-0 rounded-md border-2"
                    style={{
                      borderColor: colors.primary,
                      backgroundColor: 'transparent',
                    }}
                  />
                )}
              </button>
            );
          })}
        </div>
      );
  }
}

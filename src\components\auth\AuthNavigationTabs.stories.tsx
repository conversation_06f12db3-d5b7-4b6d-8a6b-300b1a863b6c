import type { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { AuthNavigationTabs } from './AuthNavigationTabs';
import type { LoginMode } from './LoginScreen';
import { UserIcon, LightningIcon } from '../icons';

const meta: Meta<typeof AuthNavigationTabs> = {
  title: 'Auth/AuthNavigationTabs',
  component: AuthNavigationTabs,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'Sticky authentication navigation tabs that remain accessible during scrolling.',
      },
    },
  },
  argTypes: {
    currentMode: {
      control: 'select',
      options: ['login', 'otp', 'forgot', 'access-request'],
      description: 'Currently active authentication mode',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable all tab interactions',
    },
  },
};

export default meta;
type Story = StoryObj<typeof AuthNavigationTabs>;

const InteractiveWrapper = ({ initialMode = 'login', ...args }: any) => {
  const [currentMode, setCurrentMode] = useState<LoginMode>(initialMode);

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 relative">
      {/* Simulated content to show sticky behavior */}
      <div className="p-8 space-y-8">
        <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
          Authentication Demo
        </h1>
        <div className="space-y-4">
          {Array.from({ length: 20 }, (_, i) => (
            <div
              key={i}
              className="p-4 bg-white dark:bg-slate-800 rounded-lg shadow"
            >
              <h3 className="font-semibold text-slate-900 dark:text-slate-100">
                Content Block {i + 1}
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mt-2">
                This is sample content to demonstrate the sticky navigation
                behavior. Scroll down to see how the authentication tabs remain
                fixed at the bottom.
              </p>
            </div>
          ))}
        </div>
        <div className="text-center text-slate-500 dark:text-slate-400 pb-20">
          Current mode: <strong>{currentMode}</strong>
        </div>
      </div>

      <AuthNavigationTabs
        currentMode={currentMode}
        onModeChange={setCurrentMode}
        {...args}
      />
    </div>
  );
};

export const Default: Story = {
  render: args => <InteractiveWrapper {...args} />,
};

export const LoginModeStory: Story = {
  render: args => <InteractiveWrapper initialMode="login" {...args} />,
};

export const OTPMode: Story = {
  render: args => <InteractiveWrapper initialMode="otp" {...args} />,
};

export const ForgotMode: Story = {
  render: args => <InteractiveWrapper initialMode="forgot" {...args} />,
};

export const AccessRequestMode: Story = {
  render: args => <InteractiveWrapper initialMode="access-request" {...args} />,
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
  render: args => <InteractiveWrapper {...args} />,
};

// Custom tabs example
const customTabs = [
  {
    id: 'login' as LoginMode,
    label: 'Login',
    description: 'Standard Login',
    icon: <UserIcon className="w-5 h-5" />,
  },
  {
    id: 'otp' as LoginMode,
    label: 'Quick Access',
    description: 'Phone OTP',
    icon: (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 10V3L4 14h7v7l9-11h-7z"
        />
      </svg>
    ),
  },
];

export const CustomTabs: Story = {
  args: {
    tabs: customTabs,
  },
  render: args => <InteractiveWrapper {...args} />,
};

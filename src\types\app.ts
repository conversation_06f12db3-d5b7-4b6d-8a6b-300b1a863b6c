// Core Application Types
// Centralized type definitions for core application entities

import type { ReactNode } from 'react';

// Application Environment
export type Environment = 'development' | 'staging' | 'production' | 'test';

// Application Theme
export type Theme = 'light' | 'dark' | 'system';

// Application Language
export type Language = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'zh' | 'ja';

// Time Format
export type TimeFormat = '12h' | '24h';

// Date Format
export type DateFormat = 'MM/dd/yyyy' | 'dd/MM/yyyy' | 'yyyy-MM-dd' | 'dd-MM-yyyy';

// Application Status
export type AppStatus = 'active' | 'inactive' | 'maintenance' | 'error';

// Core App Interface
export interface App {
  id: string;
  name: string;
  icon: ReactNode;
  color?: string;
  description?: string;
  version?: string;
  status?: AppStatus;
  navLinks: NavLink[];
  breadcrumbs?: Breadcrumb[];
  settings?: AppSettings;
  metadata?: AppMetadata;
}

// Navigation Link
export interface NavLink {
  id: string;
  label: string;
  href: string;
  isActive?: boolean;
  icon?: ReactNode;
  badge?: Badge;
  onClick?: (event: React.MouseEvent) => void;
  isDisabled?: boolean;
  tooltip?: string;
  children?: NavLink[];
  permissions?: string[];
}

// Breadcrumb
export interface Breadcrumb {
  id: string;
  label: string;
  href?: string;
  isActive?: boolean;
  icon?: ReactNode;
}

// Badge
export interface Badge {
  text: string;
  color?: string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  pulse?: boolean;
}

// App Settings
export interface AppSettings {
  showBreadcrumbs?: boolean;
  showNavigation?: boolean;
  maxNavLinks?: number;
  enableKeyboardShortcuts?: boolean;
  enableNotifications?: boolean;
  enableAnalytics?: boolean;
  enableErrorReporting?: boolean;
}

// App Metadata
export interface AppMetadata {
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
  tags?: string[];
  category?: string;
  priority?: number;
  dependencies?: string[];
}

// Application Registry Entry
export interface AppRegistryEntry {
  id: string;
  name: string;
  component: React.ComponentType<any>;
  icon: ReactNode;
  description?: string;
  version?: string;
  enabled?: boolean;
  permissions?: string[];
  routes?: AppRoute[];
  config?: Record<string, any>;
}

// App Route
export interface AppRoute {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  permissions?: string[];
  title?: string;
  description?: string;
}

// Application Context
export interface AppContext {
  currentApp?: App;
  availableApps: App[];
  isLoading: boolean;
  error?: string;
  permissions: string[];
  settings: AppSettings;
}

// Application Action
export interface AppAction {
  id: string;
  label: string;
  icon?: ReactNode;
  onClick: () => void;
  isPrimary?: boolean;
  isDisabled?: boolean;
  tooltip?: string;
  shortcut?: string;
  permissions?: string[];
  variant?: 'default' | 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost';
}

// Application Notification
export interface AppNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  isRead?: boolean;
  actions?: AppAction[];
  autoClose?: boolean;
  duration?: number;
}

// Application State
export interface AppState {
  currentApp: string | null;
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  notifications: AppNotification[];
  settings: AppSettings;
  metadata: Record<string, any>;
}
